Process Maps for Security Operations Processes
Current Processes

Process 5.1.1: Security Patrolling

 mermaid


 flowchart TD
      A[Identify/Classify Patrolling] --> B[Communicate Roles & Responsibilities]
      B --> C[Monitor Patrolling Performance]
      C --> D[Enhance Performance/Update Criteria]


      subgraph "Identify/Classify Patrolling"
            A1[Establish/update Patrolman selection criteria] --> A2[Review/update Patrol P
            A2 --> A3[Forecast/Provide required resources]
      end


      subgraph "Communicate Roles & Responsibilities"
          B1[Communicate implementation of Patrolman selection criteria] --> B2[Communica
      end


      subgraph "Monitor Patrolling Performance"
          C1[Ensure performing activities/responsibilities] --> C2[Check/track patrols ro
            C2 --> C3[Monitor responding time, observations & suggestions]
            C3 --> C4[Monitor Patrolmen Safety record]
            C4 --> C5[Generate patrolling activity report]
      end


      subgraph "Enhance Performance/Update Criteria"
            D1[Analyze patrolling activities] --> D2[Rotate patrolmen in different areas]
            D2 --> D3[Update Patrolling matrix/criteria/process]
      end



Process 5.1.2: Incident Handling
 mermaid

 flowchart TD
     A[Identify Incident] --> B[Respond to & Manage Incident]
     B --> C[Evaluate Security Incident Response]
     C --> D[Implement Recommendation & Share lesson learn]


     subgraph "Identify Incident"
           A1[Ensure readiness by conducting drills] --> A2[Receive/Report incident]
           A2 --> A3[Verify/collect reported information]
           A3 --> A4[Classify incident]
           A4 --> A5[Identify needed support]
     end


     subgraph "Respond to & Manage Incident"
           B1[Dispatch resources] --> B2[Notify concerned parties]
           B2 --> B3[Activate management alert systems]
           B3 --> B4[Report/record arrival time]
           B4 --> B5[Assume commander role for security incidents]
           B5 --> B6[Assess situation and execute actions]
           B6 --> B7[Update/Document the Incidents]
     end


     subgraph "Evaluate Security Incident Response"
           C1[Collect observation & information] --> C2[Participate in incident critique m
           C2 --> C3[Investigate the incidents includes root cause]
           C3 --> C4[Develop recommendations and/or action plan]
     end


     subgraph "Implement Recommendation & Share lesson learn"
           D1[Implement recommendations] --> D2[Report feedback to management]
           D2 --> D3[Share lesson learn]
           D3 --> D4[Track implementation]
     end



Process 5.1.3: Access Control
 mermaid

 flowchart TD
      A[Identify/Classify Access Area] --> B[Implement Access Control]
      B --> C[Evaluate Access Control]
      C --> D[Follow Up & Process Enhancement]


      subgraph "Identify/Classify Access Area"
            A1[Identify Access area] --> A2[Identify/Approve requirements for Security Post
      end


      subgraph "Implement Access Control"
            B1[Verify Access type] --> B2[Apply Threat level instruction]
            B2 --> B3[Implement procedures and guidelines]
            B3 --> B4[Ensure effective implementation of Visitor ID Cards]
            B4 --> B5[Provide needed resources & Training]
            B5 --> B6[Communicate New/Revision of guidelines]
      end


      subgraph "Evaluate Access Control"
            C1[Monitor Access control implementation] --> C2[Develop/Evaluate Recommendatio
            C2 --> C3[Review/update Security Post Orders]
            C3 --> C4[Review reports of compliance and QA group]
      end


      subgraph "Follow Up & Process Enhancement"
            D1[Apply valid recommendation and observation] --> D2[Enhance performance when
      end



Process 5.1.4: ID Cards & Vehicle Sticker Service
 mermaid

 flowchart TD
     A[Identify Customers] --> B[Obtain required Approval]
     B --> C[Deliver requested service]
     C --> D[Monitor Customer Satisfaction and Process Enhancement]


     subgraph "Identify Customers"
           A1[Develop/update matrix to Identify customers] --> A2[Update ID & Sticker Offi
           A2 --> A3[Update ID & Sticker Operators in ISSR-SAP System]
           A3 --> A4[Ensure readiness of ID/Sticker equipment and materials]
     end


     subgraph "Obtain required Approval"
           B1[Initiate services request] --> B2[Service request workflow completion]
           B2 --> B3[System generates initial schedule]
           B3 --> B4[System generates notification appointment]
     end


     subgraph "Deliver requested service"
           C1[Process service request] --> C2[For new ID service]
           C2 --> C3[Provide service to customer]
     end


     subgraph "Monitor Customer Satisfaction and Process Enhancement"
         D1[Capture Customer feedback] --> D2[Identify area of improvements]
           D2 --> D3[Develop recommendation and action plans]
           D3 --> D4[Review/improve the process]
           D4 --> D5[Improve service provided]
           D5 --> D6[Conduct campaigns as applicable]
     end




Proposed New Processes

New Process: Incident Response
 mermaid

 flowchart TD
     A[Identify Incident] --> B[Respond to Incident]
     B --> C[Document Response]
     C --> D[Track Performance & Improve]


     subgraph "Identify Incident"
           A1[Ensure readiness through training and drills] --> A2[Receive incident report
           A2 --> A3[Verify and collect incident information]
           A3 --> A4[Classify incident severity and type]
           A4 --> A5[Identify required response resources]
     end


     subgraph "Respond to Incident"
           B1[Dispatch appropriate resources] --> B2[Notify concerned parties]
           B2 --> B3[Activate management alert systems if required]
           B3 --> B4[Track and report arrival time]
           B4 --> B5[Establish incident command]
           B5 --> B6[Implement immediate response actions]
           B6 --> B7[Secure and stabilize the scene]
     end


     subgraph "Document Response"
           C1[Record incident details] --> C2[Document response actions taken]
           C2 --> C3[Update incident status]
           C3 --> C4[Handover to investigation team if needed]
     end


     subgraph "Track Performance & Improve"
           D1[Monitor response time KPIs] --> D2[Review response effectiveness]
           D2 --> D3[Identify improvement opportunities]
           D3 --> D4[Implement procedural enhancements]
     end



New Process: Incident Investigation
 mermaid

 flowchart TD
     A[Receive Investigation Request] --> B[Conduct Investigation]
     B --> C[Analyze Findings]
     C --> D[Report & Implement Recommendations]


     subgraph "Receive Investigation Request"
           A1[Accept investigation handover from response team] --> A2[Review initial inci
           A2 --> A3[Define investigation scope and objectives]
           A3 --> A4[Assemble investigation team]
     end


     subgraph "Conduct Investigation"
           B1[Collect evidence and information] --> B2[Interview witnesses and involved pa
           B2 --> B3[Review relevant documentation]
           B3 --> B4[Analyze systems and technical data]
           B4 --> B5[Document investigation findings]
     end


     subgraph "Analyze Findings"
         C1[Identify root causes] --> C2[Determine contributing factors]
           C2 --> C3[Assess impact and consequences]
           C3 --> C4[Develop preventive recommendations]
     end


     subgraph "Report & Implement Recommendations"
           D1[Prepare investigation report] --> D2[Present findings to management]
           D2 --> D3[Develop implementation plan for recommendations]
           D3 --> D4[Track implementation progress]
           D4 --> D5[Share lessons learned]
     end



New Process: Traffic Safety
 mermaid

 flowchart TD
     A[Define Traffic Management Requirements] --> B[Implement Traffic Controls]
     B --> C[Monitor Traffic Operations]
     C --> D[Improve Traffic Safety]


     subgraph "Define Traffic Management Requirements"
           A1[Identify traffic safety risks and needs] --> A2[Develop traffic management p
           A2 --> A3[Establish traffic control protocols]
           A3 --> A4[Define roles and responsibilities]
           A4 --> A5[Allocate resources for traffic management]
     end


     subgraph "Implement Traffic Controls"
           B1[Deploy traffic management personnel] --> B2[Install traffic control equipmen
           B2 --> B3[Implement traffic flow procedures]
           B3 --> B4[Manage special event traffic]
           B4 --> B5[Enforce traffic regulations]
     end


     subgraph "Monitor Traffic Operations"
           C1[Track traffic violations] --> C2[Monitor traffic flow and congestion]
           C2 --> C3[Collect traffic incident data]
           C3   -->   C4[Measure response to traffic events]
           C4   -->   C5[Assess effectiveness of controls]
     end


     subgraph "Improve Traffic Safety"
         D1[Analyze traffic data and trends] --> D2[Identify improvement opportunities]
           D2 --> D3[Update traffic management plans]
           D3 --> D4[Enhance traffic control mechanisms]
           D4 --> D5[Implement safety campaigns]
     end



New Process: Detection Systems Management
mermaid

flowchart TD
    A[Establish Detection System Requirements] --> B[Operate Detection Systems]
    B --> C[Monitor System Performance]
    C --> D[Maintain & Enhance Systems]


    subgraph "Establish Detection System Requirements"
          A1[Identify critical facilities requiring protection] --> A2[Define detection a
          A2 --> A3[Establish system specifications]
          A3 --> A4[Define operational protocols]
          A4 --> A5[Develop system testing procedures]
    end


    subgraph "Operate Detection Systems"
          B1[Monitor IDAS and LRDAS systems] --> B2[Process system alerts]
          B2 --> B3[Assess detection events]
          B3 --> B4[Initiate response to valid detections]
          B4 --> B5[Document system activities]
    end


    subgraph "Monitor System Performance"
          C1[Track system availability] --> C2[Measure detection accuracy]
          C2 --> C3[Assess response to system alerts]
          C3   -->   C4[Evaluate   system   effectiveness]
          C4   -->   C5[Identify   system   limitations]
    end


    subgraph "Maintain & Enhance Systems"
        D1[Perform preventive maintenance] --> D2[Troubleshoot system issues]
          D2 --> D3[Update system configurations]
          D3 --> D4[Implement system improvements]
          D4 --> D5[Train system operators]
    end
