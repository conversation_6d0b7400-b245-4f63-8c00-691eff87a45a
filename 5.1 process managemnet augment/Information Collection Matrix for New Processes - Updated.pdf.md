# Information Collection Matrix for New Processes - Updated

## 1. Incident Response Process

| Required Information | Current Source | Information Gaps | Stakeholders to Consult |
|---------------------|----------------|------------------|-------------------------|
| Process Owner | 911EC Supervisor (from existing Incident Handling) | Confirmation needed | - Director<br>- 911EC Supervisor |
| Process Expectations | Partially available in Process 5.1.2 | Need to focus specifically on response activities | - 911EC Supervisor<br>- Emergency Response Teams |
| Roles & Responsibilities | Partially available in Process 5.1.2 | Need separation of response vs. investigation roles | - Director<br>- Managers<br>- Unit Heads<br>- Emergency Response Teams |
| Process Steps | Partially available in Process 5.1.2 | Need refinement for response-specific steps | - 911EC Supervisor<br>- Emergency Response Teams |
| Performance Measures | Partially available in Process 5.1.2 | Need to include response time metrics and resource dispatch efficiency | - Director<br>- Managers<br>- QA Team |
| Reference Documents | Listed in Process 5.1.2 | May need additional response-specific references | - Document Control<br>- 911EC Supervisor |

## 2. Incident Investigation Process

| Required Information | Current Source | Information Gaps | Stakeholders to Consult |
|---------------------|----------------|------------------|-------------------------|
| Process Owner | Not formally assigned | Need to assign Security Investigation Unit Head | - Director<br>- Security Investigation Unit Head |
| Process Expectations | Partially available in Process 5.1.2 | Need to focus specifically on investigation activities | - Security Investigation Unit Head<br>- Investigation Team |
| Roles & Responsibilities | Partially available in Process 5.1.2 | Need detailed investigation roles and handover procedures | - Director<br>- Managers<br>- Security Investigation Unit Head |
| Process Steps | Partially available in Process 5.1.2 | Need detailed investigation workflow and root cause analysis methodology | - Security Investigation Unit Head<br>- Investigation Team |
| Performance Measures | Limited in Process 5.1.2 | Need investigation-specific KPIs including root cause identification rate | - Director<br>- Managers<br>- QA Team |
| Reference Documents | Partially listed in Process 5.1.2 | Need investigation-specific references and templates | - Document Control<br>- Security Investigation Unit Head |

## 3. Material Movement Control Process

| Required Information | Current Source | Information Gaps | Stakeholders to Consult |
|---------------------|----------------|------------------|-------------------------|
| Process Owner | Not formally assigned | Need to confirm ASOD Manager as owner | - Director<br>- ASOD Manager |
| Process Expectations | Minimally covered in Process 5.1.3 | Need comprehensive expectations for material movement security | - ASOD Manager<br>- Material Control Officers |
| Roles & Responsibilities | Minimally covered in Process 5.1.3 | Need detailed roles for material movement control | - Director<br>- ASOD Manager<br>- Material Control Officers |
| Process Steps | Minimally covered in Process 5.1.3 | Need complete workflow for material movement security | - ASOD Manager<br>- Material Control Officers<br>- Logistics Team |
| Performance Measures | Not available | Need comprehensive KPIs for material movement security | - Director<br>- ASOD Manager<br>- QA Team |
| Reference Documents | Limited in Process 5.1.3 | Need material movement specific documentation | - Document Control<br>- ASOD Manager |

## 4. Access Control Process (Refined)

| Required Information | Current Source | Information Gaps | Stakeholders to Consult |
|---------------------|----------------|------------------|-------------------------|
| Process Owner | ASOD Manager (from existing Access Control) | Confirmation needed | - Director<br>- ASOD Manager |
| Process Expectations | Available in Process 5.1.3 | Need refinement to focus on personnel access only | - ASOD Manager<br>- Access Control Officers |
| Roles & Responsibilities | Available in Process 5.1.3 | Need refinement to focus on personnel access only | - Director<br>- ASOD Manager<br>- Access Control Officers |
| Process Steps | Available in Process 5.1.3 | Need refinement to focus on personnel access only | - ASOD Manager<br>- Access Control Officers |
| Performance Measures | Available in Process 5.1.3 | Need refinement and expansion for comprehensive access control metrics | - Director<br>- ASOD Manager<br>- QA Team |
| Reference Documents | Listed in Process 5.1.3 | May need updates to reflect refined scope | - Document Control<br>- ASOD Manager |

## 5. Traffic Safety Process

| Required Information | Current Source | Information Gaps | Stakeholders to Consult |
|---------------------|----------------|------------------|-------------------------|
| Process Owner | Not formally assigned | Need to confirm SSOD Manager as owner | - Director<br>- SSOD Manager |
| Process Expectations | Not documented | Need comprehensive expectations for traffic safety | - SSOD Manager<br>- Traffic Safety Officers |
| Roles & Responsibilities | Not documented | Need detailed roles for traffic safety management | - Director<br>- SSOD Manager<br>- Traffic Safety Officers |
| Process Steps | Not documented | Need complete workflow for traffic management and incident response | - SSOD Manager<br>- Traffic Safety Officers |
| Performance Measures | Not available | Need comprehensive KPIs for traffic safety | - Director<br>- SSOD Manager<br>- QA Team |
| Reference Documents | Not available | Need traffic safety specific documentation | - Document Control<br>- SSOD Manager |

## 6. Detection Systems Management Process

| Required Information | Current Source | Information Gaps | Stakeholders to Consult |
|---------------------|----------------|------------------|-------------------------|
| Process Owner | Not formally assigned | Need to assign Technical Security Unit Head | - Director<br>- Technical Security Unit Head |
| Process Expectations | Not documented | Need comprehensive expectations for detection systems | - Technical Security Unit Head<br>- Technical Security Team |
| Roles & Responsibilities | Not documented | Need detailed roles for detection systems management | - Director<br>- Technical Security Unit Head<br>- Technical Security Team |
| Process Steps | Not documented | Need complete workflow for monitoring, maintenance, and response | - Technical Security Unit Head<br>- Technical Security Team |
| Performance Measures | Not available | Need comprehensive KPIs for detection systems | - Director<br>- Technical Security Unit Head<br>- QA Team |
| Reference Documents | Not available | Need detection systems specific documentation | - Document Control<br>- Technical Security Unit Head |

## 7. Security Patrolling Process (Existing - To Update)

| Required Information | Current Source | Information Gaps | Stakeholders to Consult |
|---------------------|----------------|------------------|-------------------------|
| Process Owner | SSOD Manager (from existing Security Patrolling) | Confirmation needed | - Director<br>- SSOD Manager |
| Process Expectations | Available in Process 5.1.1 | Need updates to reflect interfaces with new processes | - SSOD Manager<br>- Patrol Officers |
| Roles & Responsibilities | Available in Process 5.1.1 | Need updates to reflect interfaces with new processes | - Director<br>- SSOD Manager<br>- Patrol Officers |
| Process Steps | Available in Process 5.1.1 | Need updates to reflect interfaces with new processes | - SSOD Manager<br>- Patrol Officers |
| Performance Measures | Available in Process 5.1.1 | Need expansion to include observation quality metrics | - Director<br>- SSOD Manager<br>- QA Team |
| Reference Documents | Listed in Process 5.1.1 | May need updates to reflect new process structure | - Document Control<br>- SSOD Manager |

## 8. ID Cards & Vehicle Sticker Service Process (Existing - To Update)

| Required Information | Current Source | Information Gaps | Stakeholders to Consult |
|---------------------|----------------|------------------|-------------------------|
| Process Owner | USOD Manager (from existing ID Cards & Vehicle Sticker Service) | Confirmation needed | - Director<br>- USOD Manager |
| Process Expectations | Available in Process 5.1.4 | Need updates to reflect interfaces with Access Control | - USOD Manager<br>- ID Card Officers |
| Roles & Responsibilities | Available in Process 5.1.4 | Need updates to reflect interfaces with Access Control | - Director<br>- USOD Manager<br>- ID Card Officers |
| Process Steps | Available in Process 5.1.4 | Need updates to reflect interfaces with Access Control | - USOD Manager<br>- ID Card Officers |
| Performance Measures | Available in Process 5.1.4 | Need expansion to include customer satisfaction metrics | - Director<br>- USOD Manager<br>- QA Team |
| Reference Documents | Listed in Process 5.1.4 | May need updates to reflect new process structure | - Document Control<br>- USOD Manager |

## Information Collection Approach

1. **Document Review**
   - Review all existing process documentation
   - Identify reusable content and information gaps
   - Prepare summary of available information

2. **Stakeholder Interviews**
   - Schedule one-on-one interviews with process owners
   - Conduct focus groups with process participants
   - Document key insights and requirements

3. **Process Observation**
   - Observe current operations for each process area
   - Document actual workflows and decision points
   - Identify improvement opportunities

4. **Benchmarking**
   - Review industry best practices for each process
   - Identify applicable standards and guidelines
   - Document benchmarking findings

5. **Data Analysis**
   - Review current performance data
   - Identify data collection needs for new KPIs
   - Document data sources and collection methods

## Information Validation Process

1. **Draft Review**
   - Prepare draft information summaries
   - Circulate to key stakeholders for review
   - Document feedback and required changes

2. **Validation Workshops**
   - Conduct workshops with process teams
   - Validate collected information
   - Resolve discrepancies and conflicts

3. **Management Approval**
   - Present validated information to management
   - Obtain approval for process definitions
   - Document any required adjustments

4. **Final Documentation**
   - Incorporate all validated information
   - Prepare final information packages for each process
   - Distribute to process documentation team
