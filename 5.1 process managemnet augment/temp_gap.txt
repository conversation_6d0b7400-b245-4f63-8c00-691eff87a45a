Gap Analysis Report: Security Operations Process Enhancement
Executive Summary
This report analyzes the current state of security operations processes within SAISOD's Operational
Excellence framework and identifies gaps based on the process recommendations outlined in the
"Operational Excellence Program – Process Feedback and Recommendations" study. The analysis reveals
several opportunities to improve process structure, performance measurement, and operational
alignment that will enhance security operations effectiveness and compliance with OE Process
Management principles.

1. Current State Analysis

1.1 Existing Process Structure
The SAISOD OE Manual currently defines four specific processes under Process 5.1 (Process
Management):

 Process Number               Process Name                                      Process Owner

 5.1.1                        Security Patrolling                               SSOD Manager

 5.1.2                        Incident Handling                                 911EC Supervisor

 5.1.3                        Access Control                                    ASOD Manager

 5.1.4                        ID Cards & Vehicle Sticker Service                USOD Manager


Each process follows the standard OE process format with defined expectations, responsibilities, process
steps, and performance measures.

1.2 Current Process Limitations
 1. Incident Handling (5.1.2) combines both immediate response and investigative functions, which
    involve different responsibilities, timeframes, and objectives.
 2. Patrolling (5.1.1) includes response time as a KPI, despite this being reactive and more aligned with
    incident response than routine patrolling.

 3. Traffic Safety operations are conducted but not formalized as a distinct process despite being a
    significant aspect of security operations.

 4. Detection Systems Management (IDAS and LRDAS) lacks a structured process despite being critical
    to facility protection.

2. Gap Analysis
2.1 Process Structure Gaps

 Area               Current State                         Future State                          Gap

                    Single "Incident Handling"            Separate processes for                Need to develop two distinct
 Incident
                    process (5.1.2) combines              "Incident Response" and               processes with clear
 Management
                    response and investigation            "Incident Investigation"              boundaries and interfaces

 Traffic            No formal process exists for          Dedicated "Traffic Safety"            Need to define and document
 Management         traffic operations                    process                               a new process

 Detection          No formal process for IDAS and        Dedicated "Detection Systems          Need to define and document
 Systems            LRDAS operations                      Management" process                   a new process



2.2 Process Content Gaps
 Process            Content Gap                                                Impact

 Incident           Lack of distinction between response and                   Unclear accountability and suboptimal
 Handling           investigation activities                                   measurement

                    KPI misalignment (response time is measured                Performance metrics don't accurately reflect
 Patrolling
                    under patrolling)                                          process intent

                                                                               Lack of standardization and performance
 Traffic Safety     Missing documentation for existing operations
                                                                               measurement

 Detection                                                                     Lack of governance for critical security
                    Missing documentation for existing operations
 Systems                                                                       technology



2.3 Documentation Gaps
The following documentation gaps were identified against OE Process Management requirements:

 Documentation Requirement                       Status                  Gap

 Process Maps for proposed new processes         Not available           Need to create process maps for new processes

 SIPOC diagrams for process interfaces           Not available           Need to develop for all new and revised processes

 Roles and responsibilities matrix               Partially available     Need updates to reflect process restructuring

 KPI documentation                               Partially available     Need realignment and development of new KPIs

 Sub-process documentation                       Partially available     Need review and updates



3. Recommended Actions

3.1 Process Structure Updates
 1. Split Incident Handling: Develop two distinct processes:
        Incident Response (Process Owner: 911EC Supervisor)
        Incident Investigation (Process Owner: To be determined)

 2. Reallocate KPIs:
        Move "Patrol Response Time" from Patrolling (5.1.1) to new Incident Response process

        Retain "E-Patrol Completion" under Patrolling process

 3. Create New Processes:
        Traffic Safety process (Process Owner: To be determined)

        Detection Systems Management process (Process Owner: To be determined)


3.2 Documentation Development Needs
The following documentation must be developed in accordance with OE Process 5.1:

 1. For Each New/Revised Process:
        Process documentation using OE format
        Process maps/flowcharts

        SIPOC diagrams
        Roles and responsibilities

        Performance measures

 2. Supporting Documentation:
        Implementation plan
        Training requirements
        Communication plan


4. Alignment with OE Requirements
This proposed restructuring aligns with several key OE requirements:

 1. OE Process 5.1: "Identify, design, document, and implement Organizational Specific Processes"

 2. OE Process 5.1: "Measure, monitor, and continually improve Organizational Specific Processes"
 3. OE-12.3: "Continuous Improvement"
 4. OE-2.1: "Identify customers, needs, and measure satisfaction"

5. Next Steps
 1. Secure Director approval for proposed process changes (per section 2.1.3 of Process 5.1)
 2. Assign resources for documentation development (per section 2.1.1 of Process 5.1)
 3. Develop process documentation following the plan
 4. Review with stakeholders and subject matter experts

 5. Finalize and implement process changes

6. Timeline
The recommended timeline for implementing these changes is outlined in the accompanying Document
Preparation Plan, with full implementation expected within 12 weeks.


Prepared by: [Process Owner]


Date: April 15, 2025
