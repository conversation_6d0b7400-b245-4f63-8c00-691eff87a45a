/**
 * Custom navigation enhancements for Security Operations Restructuring presentation
 * Based on Reveal.js
 */

document.addEventListener('DOMContentLoaded', function() {
  // Wait for Reveal to be fully initialized
  Reveal.addEventListener('ready', function() {
    // Create navigation container
    createCustomNavigation();

    // Create table of contents
    createTableOfContents();

    // Create progress indicator
    enhanceProgressBar();

    // Update navigation state on slide change
    Reveal.addEventListener('slidechanged', updateNavigationState);

    // Initial update
    updateNavigationState();
  });
});

/**
 * Creates the custom navigation UI elements
 */
function createCustomNavigation() {
  // Create navigation container
  const navContainer = document.createElement('div');
  navContainer.className = 'custom-navigation';
  navContainer.setAttribute('aria-label', 'Presentation Navigation');
  navContainer.innerHTML = `
    <button id="theme-toggle" class="nav-button theme-toggle" aria-label="Toggle Dark/Light Mode">
      <span class="nav-icon">🌓</span>
      <span class="nav-label">Theme</span>
    </button>
    <button id="help-button" class="nav-button help-button" aria-label="Navigation Help">
      <span class="nav-icon">❓</span>
      <span class="nav-label">Help</span>
    </button>
    <button id="toc-button" class="nav-button toc-button" aria-label="Table of Contents">
      <span class="nav-icon">☰</span>
      <span class="nav-label">Sections</span>
    </button>
    <button id="prev-button" class="nav-button prev-button" aria-label="Previous Slide">
      <span class="nav-icon">◀</span>
      <span class="nav-label">Back</span>
    </button>
    <button id="next-button" class="nav-button next-button" aria-label="Next Slide">
      <span class="nav-label">Next</span>
      <span class="nav-icon">▶</span>
    </button>
    <button id="overview-button" class="nav-button overview-button" aria-label="Slide Overview">
      <span class="nav-icon">🔍</span>
      <span class="nav-label">Overview</span>
    </button>
  `;
  document.body.appendChild(navContainer);

  // Add event listeners
  document.getElementById('prev-button').addEventListener('click', () => Reveal.prev());
  document.getElementById('next-button').addEventListener('click', () => Reveal.next());
  document.getElementById('toc-button').addEventListener('click', toggleTableOfContents);
  document.getElementById('overview-button').addEventListener('click', () => Reveal.toggleOverview());
  document.getElementById('help-button').addEventListener('click', showNavigationHelp);
  document.getElementById('theme-toggle').addEventListener('click', toggleTheme);

  // Add restart button on the last slide
  createRestartButton();

  // Add Q&A button on later slides
  createQAButton();
}

/**
 * Creates a table of contents panel
 */
function createTableOfContents() {
  // Create TOC container
  const tocContainer = document.createElement('div');
  tocContainer.className = 'toc-container';
  tocContainer.setAttribute('aria-label', 'Table of Contents');
  tocContainer.innerHTML = `
    <div class="toc-header">
      <h3>Presentation Sections</h3>
      <button class="toc-close" aria-label="Close Table of Contents">×</button>
    </div>
    <div class="toc-content"></div>
  `;
  document.body.appendChild(tocContainer);

  // Add close button event listener
  tocContainer.querySelector('.toc-close').addEventListener('click', toggleTableOfContents);

  // Populate TOC with sections
  populateTableOfContents();
}

/**
 * Populates the table of contents with slide titles
 */
function populateTableOfContents() {
  const tocContent = document.querySelector('.toc-content');
  const slides = document.querySelectorAll('.slides section');

  // Clear existing content
  tocContent.innerHTML = '';

  // Create list of slides
  const slideList = document.createElement('ul');
  slideList.className = 'toc-list';

  // Track sections to avoid duplicates
  let sections = [];

  // Process each slide
  slides.forEach((slide, index) => {
    // Get slide title (h1, h2, or data-title attribute)
    let title = slide.querySelector('h1, h2')?.textContent ||
                slide.getAttribute('data-title') ||
                `Slide ${index + 1}`;

    // Skip duplicates (for nested slides)
    if (slide.parentNode.tagName === 'SECTION') {
      return;
    }

    // Add to sections
    sections.push({
      title: title,
      index: index
    });
  });

  // Create TOC items
  sections.forEach((section, index) => {
    const listItem = document.createElement('li');
    listItem.className = 'toc-item';
    listItem.innerHTML = `<button class="toc-link" data-index="${section.index}">${section.title}</button>`;
    slideList.appendChild(listItem);

    // Add click event
    listItem.querySelector('button').addEventListener('click', function() {
      Reveal.slide(section.index);
      toggleTableOfContents();
    });
  });

  tocContent.appendChild(slideList);
}

/**
 * Toggles the table of contents visibility
 */
function toggleTableOfContents() {
  const tocContainer = document.querySelector('.toc-container');
  tocContainer.classList.toggle('active');
}

/**
 * Enhances the progress bar with clickable sections
 */
function enhanceProgressBar() {
  const progressBar = document.querySelector('.progress');
  if (!progressBar) return;

  // Create enhanced progress bar
  const enhancedProgress = document.createElement('div');
  enhancedProgress.className = 'enhanced-progress';
  progressBar.parentNode.insertBefore(enhancedProgress, progressBar);

  // Make progress bar clickable
  progressBar.addEventListener('click', function(e) {
    const percent = e.offsetX / this.offsetWidth;
    const slideCount = Reveal.getTotalSlides();
    const targetSlide = Math.floor(percent * slideCount);
    Reveal.slide(targetSlide);
  });

  // Add tooltip to show progress
  progressBar.setAttribute('title', 'Click to navigate');
}

/**
 * Updates navigation button states based on current slide
 */
function updateNavigationState() {
  const currentSlide = Reveal.getCurrentSlide();
  const totalSlides = Reveal.getTotalSlides();
  const currentIndex = Reveal.getIndices().h;

  // Update prev/next buttons
  const prevButton = document.getElementById('prev-button');
  const nextButton = document.getElementById('next-button');

  // Disable prev button on first slide
  if (currentIndex === 0) {
    prevButton.classList.add('disabled');
    prevButton.setAttribute('aria-disabled', 'true');
  } else {
    prevButton.classList.remove('disabled');
    prevButton.setAttribute('aria-disabled', 'false');
  }

  // Change next button to "Finish" on last slide
  if (currentIndex === Reveal.getSlidesElement().childElementCount - 1) {
    nextButton.querySelector('.nav-label').textContent = 'Finish';
    nextButton.classList.add('finish-button');
  } else {
    nextButton.querySelector('.nav-label').textContent = 'Next';
    nextButton.classList.remove('finish-button');
  }
}

/**
 * Toggles between light and dark theme
 */
function toggleTheme() {
  const html = document.documentElement;
  const themeButton = document.getElementById('theme-toggle');

  // Check if dark theme is already applied
  const isDarkTheme = html.classList.contains('dark-theme');

  if (isDarkTheme) {
    // Switch to light theme
    html.classList.remove('dark-theme');
    themeButton.querySelector('.nav-icon').textContent = '🌓'; // Moon/sun icon
    themeButton.querySelector('.nav-label').textContent = 'Theme';
  } else {
    // Switch to dark theme
    html.classList.add('dark-theme');
    themeButton.querySelector('.nav-icon').textContent = '☀️'; // Sun icon
    themeButton.querySelector('.nav-label').textContent = 'Theme';
  }
}

/**
 * Shows a help modal with navigation instructions
 */
function showNavigationHelp() {
  // Create help modal if it doesn't exist
  if (!document.querySelector('.help-modal')) {
    const helpModal = document.createElement('div');
    helpModal.className = 'help-modal';
    helpModal.innerHTML = `
      <div class="help-content">
        <div class="help-header">
          <h3>Navigation Help</h3>
          <button class="help-close" aria-label="Close Help">×</button>
        </div>
        <div class="help-body">
          <h4>Navigation Buttons</h4>
          <ul>
            <li><strong>❓ Help:</strong> Show this help dialog</li>
            <li><strong>☰ Sections:</strong> Open table of contents</li>
            <li><strong>◀ Back:</strong> Go to previous slide</li>
            <li><strong>▶ Next:</strong> Go to next slide</li>
            <li><strong>🔍 Overview:</strong> Show all slides in overview mode</li>
          </ul>

          <h4>Keyboard Shortcuts</h4>
          <ul>
            <li><strong>Arrow Keys:</strong> Navigate between slides</li>
            <li><strong>Space:</strong> Next slide</li>
            <li><strong>Q:</strong> Toggle table of contents</li>
            <li><strong>O:</strong> Toggle overview mode</li>
            <li><strong>ESC:</strong> Exit overview mode</li>
          </ul>

          <h4>Other Features</h4>
          <ul>
            <li><strong>Progress Bar:</strong> Click anywhere on the progress bar to jump to that slide</li>
            <li><strong>Restart Button:</strong> Appears on the last slide to restart the presentation</li>
          </ul>
        </div>
      </div>
    `;
    document.body.appendChild(helpModal);

    // Add close button event listener
    helpModal.querySelector('.help-close').addEventListener('click', function() {
      helpModal.classList.remove('active');
    });

    // Close when clicking outside the content
    helpModal.addEventListener('click', function(e) {
      if (e.target === helpModal) {
        helpModal.classList.remove('active');
      }
    });

    // Close on ESC key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && helpModal.classList.contains('active')) {
        helpModal.classList.remove('active');
      }
    });
  }

  // Show the modal
  document.querySelector('.help-modal').classList.add('active');
}

/**
 * Creates a restart button that appears on the last slide
 */
function createRestartButton() {
  const restartButton = document.createElement('button');
  restartButton.className = 'restart-button';
  restartButton.innerHTML = `
    <span class="nav-icon">🔄</span>
    <span class="nav-label">Restart Presentation</span>
  `;
  restartButton.setAttribute('aria-label', 'Restart Presentation');
  document.body.appendChild(restartButton);

  // Add event listener
  restartButton.addEventListener('click', () => Reveal.slide(0));

  // Show only on last slide
  Reveal.addEventListener('slidechanged', function(event) {
    const totalSlides = Reveal.getTotalSlides();
    const currentIndex = Reveal.getIndices().h;

    if (currentIndex === Reveal.getSlidesElement().childElementCount - 1) {
      restartButton.classList.add('active');
    } else {
      restartButton.classList.remove('active');
    }
  });
}

/**
 * Creates a "Go to Q&A" button that appears on later slides
 */
function createQAButton() {
  // Find the Q&A slide (usually the last slide with "Questions?" text)
  let qaSlideIndex = -1;
  const slides = document.querySelectorAll('.slides section');

  slides.forEach((slide, index) => {
    // Look for a slide with "Questions" or "Q&A" in the content
    const slideContent = slide.textContent.toLowerCase();
    if (slideContent.includes('question') || slideContent.includes('q&a')) {
      qaSlideIndex = index;
    }
  });

  // If no Q&A slide found, use the last slide
  if (qaSlideIndex === -1) {
    qaSlideIndex = slides.length - 1;
  }

  // Create the button
  const qaButton = document.createElement('button');
  qaButton.className = 'qa-button';
  qaButton.innerHTML = `
    <span class="nav-icon">❓</span>
    <span class="nav-label">Go to Q&A</span>
  `;
  qaButton.setAttribute('aria-label', 'Go to Questions and Answers slide');
  document.body.appendChild(qaButton);

  // Add event listener
  qaButton.addEventListener('click', () => Reveal.slide(qaSlideIndex));

  // Show on slides near the end (last 30% of slides)
  Reveal.addEventListener('slidechanged', function(event) {
    const totalSlides = Reveal.getSlidesElement().childElementCount;
    const currentIndex = Reveal.getIndices().h;
    const threshold = Math.floor(totalSlides * 0.7); // Show on last 30% of slides

    // Show on slides near the end, but not on the actual Q&A slide
    if (currentIndex >= threshold && currentIndex !== qaSlideIndex) {
      qaButton.classList.add('active');
    } else {
      qaButton.classList.remove('active');
    }
  });
}
