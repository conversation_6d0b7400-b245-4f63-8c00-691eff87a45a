<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Restructuring Security Operations under SAISOD's Operational Excellence Framework</title>
  <link rel="stylesheet" href="lib/reveal.js/dist/reset.css">
  <link rel="stylesheet" href="lib/reveal.js/dist/reveal.css">
  <link rel="stylesheet" href="lib/reveal.js/dist/theme/white.css" id="theme">
  <link rel="stylesheet" href="custom.css">
  <link rel="stylesheet" href="custom-navigation.css">
  <style>
    .reveal {
      font-size: 32px;
    }
  </style>
</head>
<body>
  <div class="reveal">
    <div class="slides">
      <!-- Title Slide - Saudi Aramco Style -->
      <section class="title-slide">
        <div style="position: absolute; top: 20px; right: 20px; color: white; font-size: 24px; font-weight: bold; z-index: 10;">Saudi Aramco</div>
        <h1>Restructuring Security Operations</h1>
        <h3>under SAISOD's Operational Excellence Framework</h3>
        <p>Enhancing Security Operations through Process Optimization</p>
        <p><em>Presented by [Your Name]</em></p>
        <div style="position: absolute; bottom: 20px; left: 20px; font-size: 14px; color: rgba(255,255,255,0.7);">© 2023 Saudi Arabian Oil Co.</div>
      </section>

      <!-- Project Overview - Saudi Aramco Style -->
      <section class="aramco-light-bg">
        <h2>Project Overview</h2>
        <div style="position: absolute; top: 20px; right: 20px; font-size: 14px; color: var(--aramco-dark); z-index: 10;">Saudi Aramco</div>
        <ul>
          <li>SAISOD (Saudi Aramco Industrial Security Operations Department) operates under the Operational Excellence framework</li>
          <li>Current security operations structured around four primary processes</li>
          <li>Project goal: Restructure security operations processes to enhance:</li>
          <ul>
            <li>Operational clarity</li>
            <li>Performance measurement</li>
            <li>Compliance with OE principles</li>
            <li>Functional alignment</li>
          </ul>
        </ul>
      </section>

      <!-- Current State Analysis - Saudi Aramco Style -->
      <section>
        <div style="position: absolute; top: 20px; right: 20px; font-size: 14px; color: var(--aramco-dark); z-index: 10;">Saudi Aramco</div>
        <h2>Current State Analysis</h2>
        <h3>Existing Four-Process Structure</h3>
        <table>
          <tr>
            <th>Process Number</th>
            <th>Process Name</th>
            <th>Process Owner</th>
          </tr>
          <tr>
            <td><span style="color: var(--accent-color); font-weight: bold;">5.1.1</span></td>
            <td>Security Patrolling</td>
            <td>SSOD Manager</td>
          </tr>
          <tr>
            <td><span style="color: var(--accent-color); font-weight: bold;">5.1.2</span></td>
            <td>Incident Handling</td>
            <td>911EC Supervisor</td>
          </tr>
          <tr>
            <td><span style="color: var(--accent-color); font-weight: bold;">5.1.3</span></td>
            <td>Access Control</td>
            <td>ASOD Manager</td>
          </tr>
          <tr>
            <td><span style="color: var(--accent-color); font-weight: bold;">5.1.4</span></td>
            <td>ID Cards & Vehicle Sticker Service</td>
            <td>USOD Manager</td>
          </tr>
        </table>
      </section>

      <!-- Current State Limitations - Saudi Aramco Style (Enhanced) -->
      <section class="aramco-bg">
        <div style="position: absolute; top: 20px; right: 20px; font-size: 14px; color: white; z-index: 10;">Saudi Aramco</div>
        <h2>Current Process Limitations</h2>

        <!-- Two-column layout -->
        <div style="display: flex; justify-content: space-between; margin-top: 20px; max-width: 95%; margin-left: auto; margin-right: auto;">
          <!-- Left column: Visual illustration -->
          <div style="flex: 0 0 30%; display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <div style="width: 200px; height: 200px; position: relative; margin-bottom: 20px;">
              <!-- Puzzle piece illustration -->
              <div style="position: absolute; top: 0; left: 0; width: 90px; height: 90px; background-color: rgba(255,255,255,0.2); border-radius: 15px 0 0 0; border: 2px solid rgba(255,255,255,0.4);"></div>
              <div style="position: absolute; top: 0; right: 0; width: 90px; height: 90px; background-color: rgba(255,255,255,0.2); border-radius: 0 15px 0 0; border: 2px solid rgba(255,255,255,0.4);"></div>
              <div style="position: absolute; bottom: 0; left: 0; width: 90px; height: 90px; background-color: rgba(255,255,255,0.2); border-radius: 0 0 0 15px; border: 2px solid rgba(255,255,255,0.4);"></div>
              <!-- Missing puzzle piece (highlighted) -->
              <div style="position: absolute; bottom: 0; right: 0; width: 90px; height: 90px; border-radius: 0 0 15px 0; border: 2px dashed var(--accent-color); display: flex; justify-content: center; align-items: center;">
                <div style="font-size: 36px; color: var(--accent-color);">?</div>
              </div>
            </div>
            <p style="text-align: center; font-style: italic; color: rgba(255,255,255,0.7); font-size: 0.9em;">Gaps in current process structure</p>
          </div>

          <!-- Right column: Enhanced bullet points -->
          <div style="flex: 0 0 65%;">
            <ul style="list-style-type: none; padding-left: 0;">
              <!-- Most critical limitation with stronger highlighting -->
              <li class="fragment fade-in" style="margin-bottom: 15px; background-color: rgba(0,163,224,0.2); padding: 10px; border-radius: 5px; border-left: 4px solid var(--accent-color);">
                <div style="display: flex; align-items: center;">
                  <span style="font-size: 32px; margin-right: 15px; color: var(--accent-color); min-width: 40px; text-align: center;">⚠️</span>
                  <div>
                    <span style="color: white; font-weight: bold; font-size: 1.1em;">Overly Broad Processes:</span>
                    <span style="color: rgba(255,255,255,0.9);">Some processes encompass too many distinct activities</span>
                  </div>
                </div>
              </li>

              <li class="fragment fade-in" style="margin-bottom: 15px; padding: 10px; border-radius: 5px; border-left: 4px solid rgba(255,255,255,0.3);">
                <div style="display: flex; align-items: center;">
                  <span style="font-size: 32px; margin-right: 15px; color: var(--accent-color); min-width: 40px; text-align: center;">🔍</span>
                  <div>
                    <span style="color: var(--accent-color); font-weight: bold;">Incomplete Coverage:</span>
                    <span style="color: rgba(255,255,255,0.9);">Critical security functions lack dedicated process documentation</span>
                  </div>
                </div>
              </li>

              <li class="fragment fade-in" style="margin-bottom: 15px; padding: 10px; border-radius: 5px; border-left: 4px solid rgba(255,255,255,0.3);">
                <div style="display: flex; align-items: center;">
                  <span style="font-size: 32px; margin-right: 15px; color: var(--accent-color); min-width: 40px; text-align: center;">↔️</span>
                  <div>
                    <span style="color: var(--accent-color); font-weight: bold;">Unclear Boundaries:</span>
                    <span style="color: rgba(255,255,255,0.9);">Ambiguity in responsibility assignment and performance measurement</span>
                  </div>
                </div>
              </li>

              <li class="fragment fade-in" style="margin-bottom: 15px; padding: 10px; border-radius: 5px; border-left: 4px solid rgba(255,255,255,0.3);">
                <div style="display: flex; align-items: center;">
                  <span style="font-size: 32px; margin-right: 15px; color: var(--accent-color); min-width: 40px; text-align: center;">📄</span>
                  <div>
                    <span style="color: var(--accent-color); font-weight: bold;">Inconsistent Detail Level:</span>
                    <span style="color: rgba(255,255,255,0.9);">Process documentation varies in specificity</span>
                  </div>
                </div>
              </li>

              <li class="fragment fade-in" style="margin-bottom: 15px; padding: 10px; border-radius: 5px; border-left: 4px solid rgba(255,255,255,0.3);">
                <div style="display: flex; align-items: center;">
                  <span style="font-size: 32px; margin-right: 15px; color: var(--accent-color); min-width: 40px; text-align: center;">📊</span>
                  <div>
                    <span style="color: var(--accent-color); font-weight: bold;">Limited Performance Metrics:</span>
                    <span style="color: rgba(255,255,255,0.9);">Current KPIs do not adequately measure all aspects of process performance</span>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </section>

      <!-- Gap Analysis - Saudi Aramco Style -->
      <section>
        <div style="position: absolute; top: 20px; right: 20px; font-size: 14px; color: var(--aramco-dark); z-index: 10;">Saudi Aramco</div>
        <h2>Gap Analysis</h2>
        <table class="comparison-table">
          <tr>
            <th>Area</th>
            <th>Current State</th>
            <th>Desired State</th>
            <th>Gap</th>
          </tr>
          <tr class="fragment fade-in">
            <td><strong>Process Structure</strong></td>
            <td>4 processes with broad scope</td>
            <td>8 processes with focused scope</td>
            <td><span style="color: var(--accent-color);">4 new processes needed</span></td>
          </tr>
          <tr class="fragment fade-in">
            <td><strong>Documentation</strong></td>
            <td>Varying levels of detail</td>
            <td>Consistent, comprehensive documentation</td>
            <td><span style="color: var(--accent-color);">6 new/revised documents needed</span></td>
          </tr>
          <tr class="fragment fade-in">
            <td><strong>Process Ownership</strong></td>
            <td>4 process owners</td>
            <td>8 clearly defined process owners</td>
            <td><span style="color: var(--accent-color);">4 new ownership assignments</span></td>
          </tr>
          <tr class="fragment fade-in">
            <td><strong>Performance Metrics</strong></td>
            <td>Basic metrics for 4 processes</td>
            <td>Comprehensive metrics for 8 processes</td>
            <td><span style="color: var(--accent-color);">New metrics for all processes</span></td>
          </tr>
        </table>
      </section>

      <!-- Proposed Eight-Process Structure - Saudi Aramco Style (Enhanced) -->
      <section class="aramco-light-bg">
        <div style="position: absolute; top: 20px; right: 20px; font-size: 14px; color: var(--aramco-dark); z-index: 10;">Saudi Aramco</div>
        <h2>Proposed Eight-Process Structure</h2>

        <!-- Visual diagram of process structure -->
        <div class="fragment fade-in" style="text-align: center; margin: 10px auto 25px; max-width: 90%;">
          <div style="display: inline-block; padding: 8px 20px; background-color: var(--aramco-dark); color: white; font-weight: bold; border-radius: 5px; margin-bottom: 10px;">
            Security Operations Framework
          </div>
          <div style="font-size: 24px; color: var(--aramco-dark); margin: 5px 0;">↓</div>
          <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 10px;">
            <div style="width: 22%; height: 5px; background-color: var(--accent-color);"></div>
            <div style="width: 22%; height: 5px; background-color: var(--accent-color);"></div>
            <div style="width: 22%; height: 5px; background-color: var(--accent-color);"></div>
            <div style="width: 22%; height: 5px; background-color: var(--accent-color);"></div>
          </div>
        </div>

        <!-- Process cards in grid layout -->
        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin: 0 auto; max-width: 95%;">
          <!-- Process 1 -->
          <div class="fragment fade-in" style="background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 12px; border-top: 4px solid var(--aramco-dark);">
            <div style="font-size: 20px; color: var(--accent-color); margin-bottom: 5px; text-align: center;">🛡️</div>
            <h4 style="margin: 0 0 5px; color: var(--aramco-dark); font-size: 0.9em; text-align: center;">Security Patrolling</h4>
            <p style="margin: 0; font-size: 0.7em; color: var(--light-text); text-align: center;">Premises monitoring & response</p>
          </div>

          <!-- Process 2 -->
          <div class="fragment fade-in" style="background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 12px; border-top: 4px solid var(--aramco-dark);">
            <div style="font-size: 20px; color: var(--accent-color); margin-bottom: 5px; text-align: center;">🚨</div>
            <h4 style="margin: 0 0 5px; color: var(--aramco-dark); font-size: 0.9em; text-align: center;">Incident Response</h4>
            <p style="margin: 0; font-size: 0.7em; color: var(--light-text); text-align: center;">Immediate action & dispatch</p>
          </div>

          <!-- Process 3 -->
          <div class="fragment fade-in" style="background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 12px; border-top: 4px solid var(--aramco-dark);">
            <div style="font-size: 20px; color: var(--accent-color); margin-bottom: 5px; text-align: center;">🔍</div>
            <h4 style="margin: 0 0 5px; color: var(--aramco-dark); font-size: 0.9em; text-align: center;">Incident Investigation</h4>
            <p style="margin: 0; font-size: 0.7em; color: var(--light-text); text-align: center;">Root cause analysis</p>
          </div>

          <!-- Process 4 -->
          <div class="fragment fade-in" style="background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 12px; border-top: 4px solid var(--aramco-dark);">
            <div style="font-size: 20px; color: var(--accent-color); margin-bottom: 5px; text-align: center;">🔐</div>
            <h4 style="margin: 0 0 5px; color: var(--aramco-dark); font-size: 0.9em; text-align: center;">Access Control</h4>
            <p style="margin: 0; font-size: 0.7em; color: var(--light-text); text-align: center;">Permission & area monitoring</p>
          </div>

          <!-- Process 5 -->
          <div class="fragment fade-in" style="background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 12px; border-top: 4px solid var(--aramco-dark);">
            <div style="font-size: 20px; color: var(--accent-color); margin-bottom: 5px; text-align: center;">🪪</div>
            <h4 style="margin: 0 0 5px; color: var(--aramco-dark); font-size: 0.9em; text-align: center;">ID Cards & Stickers</h4>
            <p style="margin: 0; font-size: 0.7em; color: var(--light-text); text-align: center;">Credential management</p>
          </div>

          <!-- Process 6 -->
          <div class="fragment fade-in" style="background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 12px; border-top: 4px solid var(--aramco-dark);">
            <div style="font-size: 20px; color: var(--accent-color); margin-bottom: 5px; text-align: center;">🚦</div>
            <h4 style="margin: 0 0 5px; color: var(--aramco-dark); font-size: 0.9em; text-align: center;">Traffic Safety</h4>
            <p style="margin: 0; font-size: 0.7em; color: var(--light-text); text-align: center;">Flow & regulation enforcement</p>
          </div>

          <!-- Process 7 -->
          <div class="fragment fade-in" style="background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 12px; border-top: 4px solid var(--aramco-dark);">
            <div style="font-size: 20px; color: var(--accent-color); margin-bottom: 5px; text-align: center;">📡</div>
            <h4 style="margin: 0 0 5px; color: var(--aramco-dark); font-size: 0.9em; text-align: center;">Detection Systems</h4>
            <p style="margin: 0; font-size: 0.7em; color: var(--light-text); text-align: center;">Monitoring & alert processing</p>
          </div>

          <!-- Process 8 -->
          <div class="fragment fade-in" style="background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 12px; border-top: 4px solid var(--aramco-dark);">
            <div style="font-size: 20px; color: var(--accent-color); margin-bottom: 5px; text-align: center;">📦</div>
            <h4 style="margin: 0 0 5px; color: var(--aramco-dark); font-size: 0.9em; text-align: center;">Material Movement</h4>
            <p style="margin: 0; font-size: 0.7em; color: var(--light-text); text-align: center;">Secure goods transportation</p>
          </div>
        </div>

        <!-- Key benefits note -->
        <div class="fragment fade-in" style="margin: 20px auto 0; max-width: 80%; text-align: center; font-style: italic; color: var(--aramco-dark); font-size: 0.9em;">
          This structure provides clear process boundaries, specialized focus, and comprehensive coverage of all security functions
        </div>
      </section>

      <!-- Process Restructuring Rationale - Saudi Aramco Style (Enhanced) -->
      <section class="aramco-bg">
        <div style="position: absolute; top: 20px; right: 20px; font-size: 14px; color: white; z-index: 10;">Saudi Aramco</div>
        <h2>Process Restructuring Rationale</h2>

        <!-- Background watermark -->
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); opacity: 0.05; z-index: 0;">
          <div style="font-size: 300px; color: white;">⚙️</div>
        </div>

        <div style="display: flex; justify-content: space-between; margin-top: 20px; max-width: 95%; margin-left: auto; margin-right: auto; position: relative; z-index: 1;">
          <!-- Left column -->
          <div style="flex: 0 0 48%;">
            <div style="background-color: rgba(255,255,255,0.1); padding: 15px; border-radius: 5px; margin-bottom: 20px;">
              <h3 style="color: white; margin-top: 0; border-bottom: 2px solid var(--accent-color); padding-bottom: 10px; display: inline-block;">
                <span style="font-size: 24px; margin-right: 10px;">🔄</span> Why Split Incident Handling?
              </h3>

              <ul style="list-style-type: none; padding-left: 0;">
                <li class="fragment fade-in" style="margin-bottom: 12px; display: flex; align-items: center;">
                  <span style="color: var(--accent-color); font-weight: bold; margin-right: 10px;">✓</span>
                  <span style="color: rgba(255,255,255,0.9); font-size: 1.05em;">
                    <strong>Different requirements</strong>: Response and investigation need different skills, resources, and timeframes
                  </span>
                </li>

                <li class="fragment fade-in" style="margin-bottom: 12px; display: flex; align-items: center;">
                  <span style="color: var(--accent-color); font-weight: bold; margin-right: 10px;">✓</span>
                  <span style="color: rgba(255,255,255,0.9); font-size: 1.05em;">
                    <strong>Specialized metrics</strong>: Enables focused performance measurement for each function
                  </span>
                </li>

                <li class="fragment fade-in" style="margin-bottom: 12px; display: flex; align-items: center;">
                  <span style="color: var(--accent-color); font-weight: bold; margin-right: 10px;">✓</span>
                  <span style="color: rgba(255,255,255,0.9); font-size: 1.05em;">
                    <strong>Clear handover</strong>: Defines precise responsibility boundaries between teams
                  </span>
                </li>
              </ul>

              <!-- Visual diagram showing split -->
              <div class="fragment fade-in" style="margin-top: 15px; text-align: center;">
                <div style="display: inline-block; padding: 8px 15px; background-color: rgba(0,163,224,0.2); border-radius: 5px; color: white; font-weight: bold;">Incident Handling</div>
                <div style="font-size: 24px; margin: 5px 0;">↓</div>
                <div style="display: flex; justify-content: center; gap: 20px;">
                  <div style="padding: 8px 15px; background-color: rgba(0,163,224,0.4); border-radius: 5px; color: white; font-weight: bold;">Incident Response</div>
                  <div style="padding: 8px 15px; background-color: rgba(0,163,224,0.4); border-radius: 5px; color: white; font-weight: bold;">Incident Investigation</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right column -->
          <div style="flex: 0 0 48%;">
            <div style="background-color: rgba(255,255,255,0.1); padding: 15px; border-radius: 5px;">
              <h3 style="color: white; margin-top: 0; border-bottom: 2px solid var(--accent-color); padding-bottom: 10px; display: inline-block;">
                <span style="font-size: 24px; margin-right: 10px;">➕</span> Why Add New Processes?
              </h3>

              <ul style="list-style-type: none; padding-left: 0;">
                <li class="fragment fade-in" style="margin-bottom: 12px; display: flex; align-items: center;">
                  <span style="color: var(--accent-color); font-weight: bold; margin-right: 10px;">✓</span>
                  <span style="color: rgba(255,255,255,0.9); font-size: 1.05em;">
                    <strong>Documentation gap</strong>: Critical security functions lacked formal process documentation
                  </span>
                </li>

                <li class="fragment fade-in" style="margin-bottom: 12px; display: flex; align-items: center;">
                  <span style="color: var(--accent-color); font-weight: bold; margin-right: 10px;">✓</span>
                  <span style="color: rgba(255,255,255,0.9); font-size: 1.05em;">
                    <strong>Clear ownership</strong>: Enhances accountability through defined process ownership
                  </span>
                </li>

                <li class="fragment fade-in" style="margin-bottom: 12px; display: flex; align-items: center;">
                  <span style="color: var(--accent-color); font-weight: bold; margin-right: 10px;">✓</span>
                  <span style="color: rgba(255,255,255,0.9); font-size: 1.05em;">
                    <strong>Better measurement</strong>: Enables focused performance tracking and improvement
                  </span>
                </li>
              </ul>

              <!-- Visual showing new processes -->
              <div class="fragment fade-in" style="margin-top: 15px; text-align: center;">
                <div style="display: flex; justify-content: center; gap: 10px; flex-wrap: wrap;">
                  <div style="padding: 6px 10px; background-color: rgba(0,163,224,0.4); border-radius: 5px; color: white; font-weight: bold; margin-bottom: 5px;">Traffic Safety</div>
                  <div style="padding: 6px 10px; background-color: rgba(0,163,224,0.4); border-radius: 5px; color: white; font-weight: bold; margin-bottom: 5px;">Detection Systems</div>
                  <div style="padding: 6px 10px; background-color: rgba(0,163,224,0.4); border-radius: 5px; color: white; font-weight: bold; margin-bottom: 5px;">Material Movement</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Documentation & Tools - Saudi Aramco Style -->
      <section>
        <div style="position: absolute; top: 20px; right: 20px; font-size: 14px; color: var(--aramco-dark); z-index: 10;">Saudi Aramco</div>
        <h2>Documentation & Tools</h2>
        <div style="display: flex; flex-wrap: wrap; justify-content: space-between; margin-top: 30px; max-width: 95%; margin-left: auto; margin-right: auto;">
          <div style="flex: 0 0 47%; background-color: var(--secondary-color); padding: 15px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); margin-bottom: 15px;">
            <h3 style="color: var(--aramco-dark); border-bottom: 2px solid var(--accent-color); padding-bottom: 10px; margin-bottom: 15px; font-size: 1.2em;">Process Documentation</h3>
            <ul style="font-size: 0.9em;">
              <li class="fragment fade-in"><span class="highlight" style="color: var(--aramco-dark);">Process Maps:</span> Detailed flowcharts for each process</li>
              <li class="fragment fade-in"><span class="highlight" style="color: var(--aramco-dark);">Information Collection Matrices:</span> Structured stakeholder data gathering</li>
            </ul>
          </div>
          <div style="flex: 0 0 47%; background-color: var(--secondary-color); padding: 15px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); margin-bottom: 15px;">
            <h3 style="color: var(--aramco-dark); border-bottom: 2px solid var(--accent-color); padding-bottom: 10px; margin-bottom: 15px; font-size: 1.2em;">Implementation Tools</h3>
            <ul style="font-size: 0.9em;">
              <li class="fragment fade-in"><span class="highlight" style="color: var(--aramco-dark);">Implementation Plans:</span> Phased process rollout approach</li>
              <li class="fragment fade-in"><span class="highlight" style="color: var(--aramco-dark);">Performance Frameworks:</span> KPIs aligned with objectives</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- Implementation Roadmap - Saudi Aramco Style -->
      <section class="aramco-light-bg">
        <div style="position: absolute; top: 20px; right: 20px; font-size: 14px; color: var(--aramco-dark); z-index: 10;">Saudi Aramco</div>
        <h2>Implementation Roadmap</h2>

        <!-- Roadmap Introduction -->
        <div class="fragment fade-in" style="text-align: center; margin: 10px auto 25px; max-width: 80%;">
          <p style="color: var(--aramco-dark); font-style: italic;">A structured 12-week approach to transform security operations processes</p>
        </div>

        <!-- Visual roadmap -->
        <div style="margin: 0 auto 30px; max-width: 95%; position: relative;">
          <!-- Roadmap path with arrow -->
          <div style="position: relative; height: 120px; margin-bottom: 20px;">
            <!-- Main path -->
            <div style="position: absolute; top: 50%; left: 5%; right: 5%; height: 8px; background-color: #e0e0e0; border-radius: 4px; transform: translateY(-50%);"></div>

            <!-- Arrow at end -->
            <div style="position: absolute; top: 50%; right: 5%; transform: translateY(-50%) translateX(8px); width: 0; height: 0; border-top: 8px solid transparent; border-bottom: 8px solid transparent; border-left: 16px solid #e0e0e0;"></div>

            <!-- Milestone points -->
            <div class="fragment fade-in" data-fragment-index="1" style="position: absolute; top: 50%; left: 5%; width: 24px; height: 24px; background-color: var(--aramco-dark); border-radius: 50%; transform: translate(-50%, -50%); z-index: 2; box-shadow: 0 0 0 4px rgba(14,90,166,0.2);"></div>

            <div class="fragment fade-in" data-fragment-index="2" style="position: absolute; top: 50%; left: 30%; width: 24px; height: 24px; background-color: var(--accent-color); border-radius: 50%; transform: translate(-50%, -50%); z-index: 2; box-shadow: 0 0 0 4px rgba(0,163,224,0.2);"></div>

            <div class="fragment fade-in" data-fragment-index="3" style="position: absolute; top: 50%; left: 70%; width: 24px; height: 24px; background-color: var(--aramco-green); border-radius: 50%; transform: translate(-50%, -50%); z-index: 2; box-shadow: 0 0 0 4px rgba(0,149,48,0.2);"></div>

            <div class="fragment fade-in" data-fragment-index="4" style="position: absolute; top: 50%; left: 95%; width: 24px; height: 24px; background-color: var(--aramco-dark); border-radius: 50%; transform: translate(-50%, -50%); z-index: 2; box-shadow: 0 0 0 4px rgba(14,90,166,0.2);"></div>

            <!-- Milestone labels - alternating top and bottom -->
            <div class="fragment fade-in" data-fragment-index="1" style="position: absolute; top: 0; left: 5%; transform: translateX(-50%); background-color: var(--aramco-dark); color: white; padding: 5px 10px; border-radius: 4px; font-size: 0.9em; font-weight: bold; min-width: 100px; text-align: center;">
              Weeks 1-2
            </div>

            <div class="fragment fade-in" data-fragment-index="2" style="position: absolute; bottom: 0; left: 30%; transform: translateX(-50%); background-color: var(--accent-color); color: white; padding: 5px 10px; border-radius: 4px; font-size: 0.9em; font-weight: bold; min-width: 100px; text-align: center;">
              Weeks 3-8
            </div>

            <div class="fragment fade-in" data-fragment-index="3" style="position: absolute; top: 0; left: 70%; transform: translateX(-50%); background-color: var(--aramco-green); color: white; padding: 5px 10px; border-radius: 4px; font-size: 0.9em; font-weight: bold; min-width: 100px; text-align: center;">
              Weeks 9-10
            </div>

            <div class="fragment fade-in" data-fragment-index="4" style="position: absolute; bottom: 0; left: 95%; transform: translateX(-50%); background-color: var(--aramco-dark); color: white; padding: 5px 10px; border-radius: 4px; font-size: 0.9em; font-weight: bold; min-width: 100px; text-align: center;">
              Weeks 11-12
            </div>

            <!-- Progress segments -->
            <div class="fragment fade-in" data-fragment-index="1" style="position: absolute; top: 50%; left: 5%; width: 25%; height: 8px; background-color: var(--aramco-dark); border-radius: 4px 0 0 4px; transform: translateY(-50%);"></div>

            <div class="fragment fade-in" data-fragment-index="2" style="position: absolute; top: 50%; left: 30%; width: 40%; height: 8px; background-color: var(--accent-color); transform: translateY(-50%);"></div>

            <div class="fragment fade-in" data-fragment-index="3" style="position: absolute; top: 50%; left: 70%; width: 25%; height: 8px; background-color: var(--aramco-green); border-radius: 0 4px 4px 0; transform: translateY(-50%);"></div>
          </div>
        </div>

        <!-- Phase details -->
        <div style="display: flex; flex-direction: column; gap: 15px; margin: 0 auto; max-width: 95%;">
          <!-- Phase 1 -->
          <div class="fragment fade-in" data-fragment-index="1" style="background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 15px; border-left: 5px solid var(--aramco-dark); display: flex; align-items: flex-start;">
            <div style="flex: 0 0 auto; margin-right: 15px;">
              <div style="width: 40px; height: 40px; background-color: var(--aramco-dark); border-radius: 50%; color: white; display: flex; justify-content: center; align-items: center; font-weight: bold; font-size: 1.2em;">1</div>
            </div>
            <div style="flex: 1 1 auto;">
              <h3 style="margin: 0 0 10px; color: var(--aramco-dark); display: flex; align-items: center;">
                <span>Gap Analysis & Planning</span>
                <span style="margin-left: 10px; font-size: 0.7em; font-weight: normal; color: var(--light-text); background-color: rgba(14,90,166,0.1); padding: 3px 8px; border-radius: 12px;">Weeks 1-2</span>
              </h3>
              <p style="margin: 0 0 10px; color: var(--light-text); font-size: 0.9em;">Establish project foundation and identify improvement opportunities</p>
              <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                <div style="background-color: rgba(14,90,166,0.05); padding: 8px 12px; border-radius: 4px; font-size: 0.85em; display: flex; align-items: center;">
                  <span style="color: var(--aramco-dark); margin-right: 5px;">📋</span>
                  <span>Review existing documentation</span>
                </div>
                <div style="background-color: rgba(14,90,166,0.05); padding: 8px 12px; border-radius: 4px; font-size: 0.85em; display: flex; align-items: center;">
                  <span style="color: var(--aramco-dark); margin-right: 5px;">👥</span>
                  <span>Identify key stakeholders</span>
                </div>
                <div style="background-color: rgba(14,90,166,0.05); padding: 8px 12px; border-radius: 4px; font-size: 0.85em; display: flex; align-items: center;">
                  <span style="color: var(--aramco-dark); margin-right: 5px;">🔍</span>
                  <span>Plan resource requirements</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Phase 2 -->
          <div class="fragment fade-in" data-fragment-index="2" style="background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 15px; border-left: 5px solid var(--accent-color); display: flex; align-items: flex-start;">
            <div style="flex: 0 0 auto; margin-right: 15px;">
              <div style="width: 40px; height: 40px; background-color: var(--accent-color); border-radius: 50%; color: white; display: flex; justify-content: center; align-items: center; font-weight: bold; font-size: 1.2em;">2</div>
            </div>
            <div style="flex: 1 1 auto;">
              <h3 style="margin: 0 0 10px; color: var(--aramco-dark); display: flex; align-items: center;">
                <span>Process Definition & Documentation</span>
                <span style="margin-left: 10px; font-size: 0.7em; font-weight: normal; color: var(--light-text); background-color: rgba(0,163,224,0.1); padding: 3px 8px; border-radius: 12px;">Weeks 3-8</span>
              </h3>
              <p style="margin: 0 0 10px; color: var(--light-text); font-size: 0.9em;">Design and document the eight security processes in detail</p>
              <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                <div style="background-color: rgba(0,163,224,0.05); padding: 8px 12px; border-radius: 4px; font-size: 0.85em; display: flex; align-items: center;">
                  <span style="color: var(--accent-color); margin-right: 5px;">🔄</span>
                  <span>Design detailed process flows</span>
                </div>
                <div style="background-color: rgba(0,163,224,0.05); padding: 8px 12px; border-radius: 4px; font-size: 0.85em; display: flex; align-items: center;">
                  <span style="color: var(--accent-color); margin-right: 5px;">📝</span>
                  <span>Develop comprehensive documentation</span>
                </div>
                <div style="background-color: rgba(0,163,224,0.05); padding: 8px 12px; border-radius: 4px; font-size: 0.85em; display: flex; align-items: center;">
                  <span style="color: var(--accent-color); margin-right: 5px;">📊</span>
                  <span>Define performance metrics (KPIs)</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Phase 3 -->
          <div class="fragment fade-in" data-fragment-index="3" style="background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 15px; border-left: 5px solid var(--aramco-green); display: flex; align-items: flex-start;">
            <div style="flex: 0 0 auto; margin-right: 15px;">
              <div style="width: 40px; height: 40px; background-color: var(--aramco-green); border-radius: 50%; color: white; display: flex; justify-content: center; align-items: center; font-weight: bold; font-size: 1.2em;">3</div>
            </div>
            <div style="flex: 1 1 auto;">
              <h3 style="margin: 0 0 10px; color: var(--aramco-dark); display: flex; align-items: center;">
                <span>Implementation Planning</span>
                <span style="margin-left: 10px; font-size: 0.7em; font-weight: normal; color: var(--light-text); background-color: rgba(0,149,48,0.1); padding: 3px 8px; border-radius: 12px;">Weeks 9-10</span>
              </h3>
              <p style="margin: 0 0 10px; color: var(--light-text); font-size: 0.9em;">Prepare for successful rollout of the new process structure</p>
              <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                <div style="background-color: rgba(0,149,48,0.05); padding: 8px 12px; border-radius: 4px; font-size: 0.85em; display: flex; align-items: center;">
                  <span style="color: var(--aramco-green); margin-right: 5px;">📚</span>
                  <span>Develop training materials</span>
                </div>
                <div style="background-color: rgba(0,149,48,0.05); padding: 8px 12px; border-radius: 4px; font-size: 0.85em; display: flex; align-items: center;">
                  <span style="color: var(--aramco-green); margin-right: 5px;">📢</span>
                  <span>Create communication plans</span>
                </div>
                <div style="background-color: rgba(0,149,48,0.05); padding: 8px 12px; border-radius: 4px; font-size: 0.85em; display: flex; align-items: center;">
                  <span style="color: var(--aramco-green); margin-right: 5px;">📅</span>
                  <span>Establish implementation schedules</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Phase 4 -->
          <div class="fragment fade-in" data-fragment-index="4" style="background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 15px; border-left: 5px solid var(--aramco-dark); display: flex; align-items: flex-start;">
            <div style="flex: 0 0 auto; margin-right: 15px;">
              <div style="width: 40px; height: 40px; background-color: var(--aramco-dark); border-radius: 50%; color: white; display: flex; justify-content: center; align-items: center; font-weight: bold; font-size: 1.2em;">4</div>
            </div>
            <div style="flex: 1 1 auto;">
              <h3 style="margin: 0 0 10px; color: var(--aramco-dark); display: flex; align-items: center;">
                <span>Finalization & Handover</span>
                <span style="margin-left: 10px; font-size: 0.7em; font-weight: normal; color: var(--light-text); background-color: rgba(14,90,166,0.1); padding: 3px 8px; border-radius: 12px;">Weeks 11-12</span>
              </h3>
              <p style="margin: 0 0 10px; color: var(--light-text); font-size: 0.9em;">Complete documentation and transition to operational teams</p>
              <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                <div style="background-color: rgba(14,90,166,0.05); padding: 8px 12px; border-radius: 4px; font-size: 0.85em; display: flex; align-items: center;">
                  <span style="color: var(--aramco-dark); margin-right: 5px;">✅</span>
                  <span>Finalize all documentation</span>
                </div>
                <div style="background-color: rgba(14,90,166,0.05); padding: 8px 12px; border-radius: 4px; font-size: 0.85em; display: flex; align-items: center;">
                  <span style="color: var(--aramco-dark); margin-right: 5px;">👍</span>
                  <span>Obtain necessary approvals</span>
                </div>
                <div style="background-color: rgba(14,90,166,0.05); padding: 8px 12px; border-radius: 4px; font-size: 0.85em; display: flex; align-items: center;">
                  <span style="color: var(--aramco-dark); margin-right: 5px;">🔄</span>
                  <span>Transfer to operations teams</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Expected Outcomes - Saudi Aramco Style (Enhanced) -->
      <section class="aramco-bg">
        <div style="position: absolute; top: 20px; right: 20px; font-size: 14px; color: white; z-index: 10;">Saudi Aramco</div>
        <h2>Expected Outcomes</h2>

        <!-- Background watermark -->
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); opacity: 0.05; z-index: 0;">
          <div style="font-size: 300px; color: white;">📈</div>
        </div>

        <!-- Intro text -->
        <div class="fragment fade-in" style="text-align: center; margin: 0 auto 30px; max-width: 80%; position: relative; z-index: 1;">
          <p style="color: white; font-style: italic; font-size: 1.1em;">
            The restructuring initiative will deliver significant improvements across multiple dimensions:
          </p>
        </div>

        <!-- Outcome cards in a hexagonal-inspired layout -->
        <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 20px; margin: 0 auto; max-width: 95%; position: relative; z-index: 1;">
          <!-- Outcome 1 -->
          <div class="fragment fade-in" style="flex: 0 0 30%; background: linear-gradient(135deg, rgba(0,163,224,0.2) 0%, rgba(0,163,224,0.4) 100%); padding: 20px; border-radius: 10px; text-align: center; transform: translateY(-15px);">
            <div style="font-size: 40px; color: white; margin-bottom: 10px;">🎯</div>
            <h3 style="color: white; margin: 0 0 10px; font-size: 1.1em; border-bottom: 2px solid var(--accent-color); padding-bottom: 8px; display: inline-block;">
              Improved Clarity
            </h3>
            <p style="margin: 0; color: rgba(255,255,255,0.9); font-size: 0.9em;">Clear process boundaries and ownership definition</p>
            <div style="margin-top: 15px; background-color: rgba(255,255,255,0.1); padding: 8px; border-radius: 5px; font-size: 0.8em; color: var(--accent-color);">
              <strong>Key Benefit:</strong> Reduced overlap and gaps in responsibilities
            </div>
          </div>

          <!-- Outcome 2 -->
          <div class="fragment fade-in" style="flex: 0 0 30%; background: linear-gradient(135deg, rgba(0,163,224,0.2) 0%, rgba(0,163,224,0.4) 100%); padding: 20px; border-radius: 10px; text-align: center;">
            <div style="font-size: 40px; color: white; margin-bottom: 10px;">📊</div>
            <h3 style="color: white; margin: 0 0 10px; font-size: 1.1em; border-bottom: 2px solid var(--accent-color); padding-bottom: 8px; display: inline-block;">
              Enhanced Measurement
            </h3>
            <p style="margin: 0; color: rgba(255,255,255,0.9); font-size: 0.9em;">Focused KPIs tailored to each specific process</p>
            <div style="margin-top: 15px; background-color: rgba(255,255,255,0.1); padding: 8px; border-radius: 5px; font-size: 0.8em; color: var(--accent-color);">
              <strong>Key Benefit:</strong> More accurate performance tracking
            </div>
          </div>

          <!-- Outcome 3 -->
          <div class="fragment fade-in" style="flex: 0 0 30%; background: linear-gradient(135deg, rgba(0,163,224,0.2) 0%, rgba(0,163,224,0.4) 100%); padding: 20px; border-radius: 10px; text-align: center; transform: translateY(-15px);">
            <div style="font-size: 40px; color: white; margin-bottom: 10px;">📋</div>
            <h3 style="color: white; margin: 0 0 10px; font-size: 1.1em; border-bottom: 2px solid var(--accent-color); padding-bottom: 8px; display: inline-block;">
              OE Compliance
            </h3>
            <p style="margin: 0; color: rgba(255,255,255,0.9); font-size: 0.9em;">Standardized documentation and approach</p>
            <div style="margin-top: 15px; background-color: rgba(255,255,255,0.1); padding: 8px; border-radius: 5px; font-size: 0.8em; color: var(--accent-color);">
              <strong>Key Benefit:</strong> Alignment with organizational standards
            </div>
          </div>

          <!-- Outcome 4 -->
          <div class="fragment fade-in" style="flex: 0 0 30%; background: linear-gradient(135deg, rgba(0,163,224,0.2) 0%, rgba(0,163,224,0.4) 100%); padding: 20px; border-radius: 10px; text-align: center; transform: translateY(15px);">
            <div style="font-size: 40px; color: white; margin-bottom: 10px;">⚡</div>
            <h3 style="color: white; margin: 0 0 10px; font-size: 1.1em; border-bottom: 2px solid var(--accent-color); padding-bottom: 8px; display: inline-block;">
              Operational Efficiency
            </h3>
            <p style="margin: 0; color: rgba(255,255,255,0.9); font-size: 0.9em;">Streamlined processes with clear interfaces</p>
            <div style="margin-top: 15px; background-color: rgba(255,255,255,0.1); padding: 8px; border-radius: 5px; font-size: 0.8em; color: var(--accent-color);">
              <strong>Key Benefit:</strong> Faster response times and reduced duplication
            </div>
          </div>

          <!-- Outcome 5 -->
          <div class="fragment fade-in" style="flex: 0 0 30%; background: linear-gradient(135deg, rgba(0,163,224,0.2) 0%, rgba(0,163,224,0.4) 100%); padding: 20px; border-radius: 10px; text-align: center; transform: translateY(15px);">
            <div style="font-size: 40px; color: white; margin-bottom: 10px;">🛡️</div>
            <h3 style="color: white; margin: 0 0 10px; font-size: 1.1em; border-bottom: 2px solid var(--accent-color); padding-bottom: 8px; display: inline-block;">
              Risk Management
            </h3>
            <p style="margin: 0; color: rgba(255,255,255,0.9); font-size: 0.9em;">Comprehensive coverage of security functions</p>
            <div style="margin-top: 15px; background-color: rgba(255,255,255,0.1); padding: 8px; border-radius: 5px; font-size: 0.8em; color: var(--accent-color);">
              <strong>Key Benefit:</strong> Reduced security vulnerabilities
            </div>
          </div>
        </div>

        <!-- Bottom note -->
        <div class="fragment fade-in" style="text-align: center; margin: 30px auto 0; max-width: 80%; position: relative; z-index: 1;">
          <div style="display: inline-block; background-color: rgba(255,255,255,0.1); padding: 10px 20px; border-radius: 5px; border-left: 4px solid var(--accent-color);">
            <p style="margin: 0; color: white; font-weight: bold;">
              Overall Impact: Enhanced security posture and operational excellence
            </p>
          </div>
        </div>
      </section>

      <!-- Closing Slide - Saudi Aramco Style -->
      <section class="title-slide">
        <div style="position: absolute; top: 20px; right: 20px; color: white; font-size: 24px; font-weight: bold; z-index: 10;">Saudi Aramco</div>
        <h2>Moving Forward</h2>
        <p>This restructuring initiative will:</p>
        <div style="display: flex; flex-wrap: wrap; justify-content: space-between; margin: 20px auto; max-width: 95%;">
          <div class="fragment fade-in" style="flex: 0 0 31%; background-color: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; text-align: center; margin-bottom: 10px;">
            <div style="font-size: 28px; color: var(--accent-color); margin-bottom: 5px;">✓</div>
            <p style="margin: 0; font-size: 0.9em;">Enhance security operations effectiveness</p>
          </div>
          <div class="fragment fade-in" style="flex: 0 0 31%; background-color: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; text-align: center; margin-bottom: 10px;">
            <div style="font-size: 28px; color: var(--accent-color); margin-bottom: 5px;">✓</div>
            <p style="margin: 0; font-size: 0.9em;">Improve accountability and measurement</p>
          </div>
          <div class="fragment fade-in" style="flex: 0 0 31%; background-color: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; text-align: center; margin-bottom: 10px;">
            <div style="font-size: 28px; color: var(--accent-color); margin-bottom: 5px;">✓</div>
            <p style="margin: 0; font-size: 0.9em;">Align with excellence principles</p>
          </div>
        </div>
        <div class="fragment fade-in" style="background-color: rgba(255,255,255,0.1); padding: 15px; border-radius: 5px; margin-top: 20px;">
          <p style="margin: 0; font-weight: bold;">Next Steps: Secure approval to begin Phase 1 implementation</p>
        </div>
        <p style="margin-top: 2em; font-size: 1.5em; font-weight: bold;">Questions?</p>
        <div style="position: absolute; bottom: 20px; left: 20px; font-size: 14px; color: rgba(255,255,255,0.7);">© 2023 Saudi Arabian Oil Co.</div>
      </section>
    </div>
  </div>

  <script src="lib/reveal.js/dist/reveal.js"></script>
  <script src="lib/reveal.js/plugin/zoom/zoom.js"></script>
  <script src="lib/reveal.js/plugin/notes/notes.js"></script>
  <script src="lib/reveal.js/plugin/highlight/highlight.js"></script>
  <script src="custom-navigation.js"></script>
  <script>
    Reveal.initialize({
      controls: false, // Disable default controls as we're using custom ones
      progress: true,
      center: true,
      hash: true,
      transition: 'slide',
      slideNumber: true,
      overview: true,
      touch: true,
      hideInactiveCursor: true,
      navigationMode: 'default',
      // Ensure content fits on screen
      width: "100%",
      height: "100%",
      margin: 0.1,
      minScale: 0.5,
      maxScale: 1.5,
      // Add keyboard shortcuts for accessibility
      keyboard: {
        // Custom keyboard shortcuts
        81: function() { toggleTableOfContents(); }, // 'q' key for TOC
        79: function() { Reveal.toggleOverview(); }, // 'o' key for overview
      },
      // Learn about plugins: https://revealjs.com/plugins/
      plugins: [ RevealZoom, RevealNotes, RevealHighlight ]
    });

    // Add slide titles for better navigation
    document.querySelectorAll('.slides section').forEach(function(slide, index) {
      // If slide doesn't have a title attribute, add one based on content
      if (!slide.hasAttribute('data-title')) {
        const heading = slide.querySelector('h1, h2, h3');
        if (heading) {
          slide.setAttribute('data-title', heading.textContent);
        } else {
          slide.setAttribute('data-title', 'Slide ' + (index + 1));
        }
      }
    });
  </script>
</body>
</html>
