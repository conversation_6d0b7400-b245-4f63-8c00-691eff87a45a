#!/bin/bash

# Simple script to start a local server for the presentation

echo "Starting local server for Security Operations Restructuring Presentation..."
echo "Once the server starts, open http://localhost:8000 in your browser"
echo "Press Ctrl+C to stop the server when finished"
echo ""

# Check if Python 3 is available
if command -v python3 &>/dev/null; then
    python3 -m http.server
# Check if Python 2 is available
elif command -v python &>/dev/null; then
    python -m SimpleHTTPServer
# If no Python, try using Node's http-server if installed
elif command -v npx &>/dev/null; then
    npx http-server -p 8000
else
    echo "Error: Could not find Python or Node.js http-server."
    echo "Please install Python or Node.js, or manually open presentation.html in your browser."
    exit 1
fi
