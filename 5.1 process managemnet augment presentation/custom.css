/* Custom CSS for Security Operations Restructuring Presentation - Saudi Aramco Style */

/* Main theme colors based on Saudi Aramco's website */
:root {
  --primary-color: #0e5aa6; /* Aramco blue */
  --secondary-color: #f2f2f2; /* Light gray background */
  --accent-color: #00a3e0; /* Aramco light blue accent */
  --text-color: #333333; /* Dark text */
  --light-text: #666666; /* Secondary text */
  --border-color: #dddddd; /* Border color */
  --aramco-green: #009530; /* Aramco green for highlights */
  --aramco-dark: #002c5f; /* Aramco dark blue */
}

/* Global styles - Saudi Aramco style */
.reveal {
  font-family: 'Arial', 'Helvetica', sans-serif; /* Aramco uses a clean sans-serif font */
  color: var(--text-color);
  background-color: white;
}

/* Fix for content overflow */
.reveal .slides {
  width: 100% !important;
  height: 100% !important;
}

.reveal .slides section {
  width: 100% !important;
  height: 100% !important;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
}

.reveal h1,
.reveal h2,
.reveal h3,
.reveal h4 {
  font-family: 'Arial', 'Helvetica', sans-serif;
  color: var(--aramco-dark);
  text-transform: none;
  letter-spacing: normal;
  font-weight: 600;
}

.reveal h2 {
  border-bottom: 3px solid var(--accent-color);
  padding-bottom: 10px;
  display: inline-block;
}

/* Title slide - Aramco style */
.title-slide {
  background: linear-gradient(135deg, var(--aramco-dark) 0%, var(--primary-color) 100%);
  padding: 30px;
  color: white;
  text-align: left;
}

.title-slide h1 {
  color: white;
  font-size: 2.5em;
  margin-bottom: 0.2em;
  font-weight: 700;
}

.title-slide h3 {
  color: white;
  font-size: 1.5em;
  margin-bottom: 1.5em;
  opacity: 0.9;
}

.title-slide p {
  color: white;
}

/* Aramco logo for title slide */
.aramco-logo {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 120px;
  height: auto;
}

/* Lists - Aramco style */
.reveal ul li,
.reveal ol li {
  margin-bottom: 0.8em;
  line-height: 1.5;
  position: relative;
}

.reveal ul {
  list-style-type: none;
}

.reveal ul li:before {
  content: "•";
  color: var(--accent-color);
  font-weight: bold;
  display: inline-block;
  width: 1em;
  margin-left: -1em;
  position: relative;
  top: -0.1em;
}

/* Tables - Aramco style */
.reveal table {
  border-collapse: collapse;
  width: 90%;
  margin: 1em auto;
  font-size: 0.8em;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.reveal table th {
  background-color: var(--aramco-dark);
  color: white;
  font-weight: bold;
  padding: 0.8em;
  text-align: left;
  border: none;
}

.reveal table td {
  padding: 0.8em;
  border-bottom: 1px solid var(--border-color);
  text-align: left;
}

.reveal table tr:nth-child(even) {
  background-color: var(--secondary-color);
}

.reveal table tr:hover {
  background-color: rgba(0,163,224,0.1);
}

/* Comparison table */
.comparison-table {
  font-size: 0.7em;
}

/* Process list */
.process-list li {
  margin-bottom: 1em;
  position: relative;
  display: flex;
  align-items: flex-start;
}

/* Highlight text - Aramco style */
.highlight {
  color: var(--aramco-dark);
  font-weight: bold;
}

/* Two-column layout */
.two-column {
  display: flex;
  justify-content: space-between;
}

.two-column ul {
  flex: 0 0 48%;
}

/* Timeline - Aramco style */
.timeline {
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  max-width: 90%;
}

.timeline-item {
  display: flex;
  margin-bottom: 1.2em;
  border-left: 4px solid var(--accent-color);
  padding-left: 1em;
  background-color: rgba(242,242,242,0.5);
  padding: 15px 15px 15px 20px;
  border-radius: 0 5px 5px 0;
}

.timeline-phase {
  flex: 0 0 30%;
  font-weight: bold;
  color: var(--aramco-dark);
}

.timeline-content {
  flex: 0 0 70%;
}

/* Transitions */
.reveal .slides section .fragment.highlight-blue.visible {
  color: var(--accent-color);
}

/* Custom backgrounds - Aramco style */
.aramco-bg {
  background: linear-gradient(135deg, rgba(0,44,95,0.9) 0%, rgba(14,90,166,0.8) 100%);
  color: white;
}

.aramco-bg h2,
.aramco-bg h3 {
  color: white;
  border-bottom: 2px solid var(--accent-color);
  padding-bottom: 10px;
  display: inline-block;
}

.aramco-light-bg {
  background-color: var(--secondary-color);
}

/* Progress bar - Aramco style */
.reveal .progress {
  height: 4px;
  color: var(--accent-color);
}

/* Controls - Aramco style */
.reveal .controls {
  color: var(--accent-color);
}

/* Buttons - Aramco style */
.aramco-button {
  display: inline-block;
  background-color: var(--accent-color);
  color: white;
  padding: 10px 20px;
  border-radius: 3px;
  font-weight: bold;
  margin-top: 15px;
  transition: background-color 0.3s;
}

.aramco-button:hover {
  background-color: var(--aramco-dark);
}

/* Print styles */
@media print {
  .reveal .slides {
    height: auto !important;
  }

  .reveal .slides > section {
    min-height: auto !important;
    page-break-after: always;
  }
}
