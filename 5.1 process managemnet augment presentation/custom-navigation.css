/**
 * Custom navigation styles for Security Operations Restructuring presentation
 * Based on Saudi Aramco styling
 */

/* Hide default controls */
.reveal .controls {
  display: none !important;
}

/* Custom navigation container */
.custom-navigation {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
  z-index: 1000;
  transition: opacity 0.3s ease;
}

/* Base button styles */
.nav-button {
  background-color: var(--aramco-dark, #002c5f);
  color: white;
  border: none;
  border-radius: 30px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  outline: none;
  min-width: 100px;
}

/* Button hover effects */
.nav-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  background-color: var(--accent-color, #00a3e0);
}

/* Button active effects */
.nav-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Button focus styles for accessibility */
.nav-button:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Button icons */
.nav-icon {
  font-size: 16px;
  display: inline-block;
}

/* Button labels */
.nav-label {
  margin: 0 5px;
}

/* Previous button styling */
.prev-button {
  background-color: rgba(0, 44, 95, 0.8);
}

.prev-button .nav-icon {
  margin-right: 5px;
}

/* Next button styling */
.next-button {
  background-color: var(--accent-color, #00a3e0);
}

.next-button .nav-icon {
  margin-left: 5px;
}

/* Finish button styling (last slide) */
.finish-button {
  background-color: var(--aramco-green, #009530) !important;
}

/* TOC button styling */
.toc-button {
  background-color: rgba(0, 44, 95, 0.7);
}

/* Overview button styling */
.overview-button {
  background-color: rgba(0, 44, 95, 0.7);
}

/* Disabled button state */
.nav-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.nav-button.disabled:hover {
  transform: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* Table of Contents container */
.toc-container {
  position: fixed;
  top: 0;
  left: -300px;
  width: 300px;
  height: 100%;
  background-color: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
  z-index: 1001;
  transition: left 0.3s ease;
  display: flex;
  flex-direction: column;
}

/* Active TOC */
.toc-container.active {
  left: 0;
}

/* TOC header */
.toc-header {
  background-color: var(--aramco-dark, #002c5f);
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toc-header h3 {
  margin: 0;
  font-size: 18px;
  color: white !important;
}

/* TOC close button */
.toc-close {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

/* TOC content area */
.toc-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

/* TOC list */
.toc-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.toc-item {
  margin-bottom: 10px;
}

/* TOC links */
.toc-link {
  display: block;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  color: var(--aramco-dark, #002c5f);
  text-decoration: none;
  transition: all 0.2s ease;
  border: none;
  text-align: left;
  width: 100%;
  cursor: pointer;
  font-size: 14px;
}

.toc-link:hover {
  background-color: #e0e0e0;
  transform: translateX(5px);
}

/* Enhanced progress bar */
.enhanced-progress {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 10px;
  z-index: 999;
}

/* Make the progress bar more visible */
.reveal .progress {
  height: 10px !important;
  cursor: pointer;
}

.reveal .progress span {
  background-color: var(--accent-color, #00a3e0) !important;
}

/* Restart button */
.restart-button {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background-color: var(--aramco-green, #009530);
  color: white;
  border: none;
  border-radius: 30px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
  z-index: 1000;
}

.restart-button.active {
  opacity: 1;
  transform: translateY(0);
}

.restart-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Q&A button */
.qa-button {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%) translateY(20px);
  background-color: var(--accent-color, #00a3e0);
  color: white;
  border: none;
  border-radius: 30px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  opacity: 0;
  z-index: 1000;
}

.qa-button.active {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.qa-button:hover {
  background-color: var(--aramco-dark, #002c5f);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.qa-button .nav-icon {
  margin-right: 8px;
}

/* Theme toggle button */
.theme-toggle {
  background-color: rgba(0, 44, 95, 0.7);
}

/* Help button styling */
.help-button {
  background-color: rgba(0, 44, 95, 0.7);
}

/* Dark theme styles */
html.dark-theme {
  filter: invert(90%) hue-rotate(180deg);
}

html.dark-theme img,
html.dark-theme video,
html.dark-theme .custom-navigation,
html.dark-theme .toc-container,
html.dark-theme .help-modal,
html.dark-theme .restart-button,
html.dark-theme .qa-button {
  filter: invert(100%) hue-rotate(180deg);
}

/* Help modal */
.help-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.help-modal.active {
  opacity: 1;
  visibility: visible;
}

.help-content {
  background-color: white;
  width: 500px;
  max-width: 90%;
  max-height: 80vh;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.help-header {
  background-color: var(--aramco-dark, #002c5f);
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.help-header h3 {
  margin: 0;
  font-size: 18px;
  color: white !important;
}

.help-close {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.help-body {
  padding: 20px;
  overflow-y: auto;
}

.help-body h4 {
  color: var(--aramco-dark, #002c5f);
  margin: 15px 0 10px;
  font-size: 16px;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.help-body h4:first-child {
  margin-top: 0;
}

.help-body ul {
  padding-left: 20px;
  margin: 10px 0;
}

.help-body li {
  margin-bottom: 5px;
  font-size: 14px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .nav-button {
    min-width: auto;
    padding: 8px 12px;
  }

  .nav-label {
    display: none;
  }

  .nav-icon {
    margin: 0 !important;
  }

  .toc-container {
    width: 250px;
    left: -250px;
  }

  .help-content {
    width: 90%;
    max-height: 70vh;
  }
}
