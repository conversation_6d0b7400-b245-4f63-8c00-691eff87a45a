<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Operations Restructuring Project</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2a5caa;
        }
        .container {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .button {
            display: inline-block;
            background-color: #2a5caa;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        .button:hover {
            background-color: #1e4785;
        }
        .files {
            background-color: #fff;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .files h3 {
            margin-top: 0;
        }
        .files ul {
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Security Operations Restructuring Project</h1>
        <p>Welcome to the presentation portal for the "Restructuring Security Operations under SAISOD's Operational Excellence Framework" project.</p>

        <h2>About This Project</h2>
        <p>This project focuses on enhancing security operations through process optimization, restructuring the current four-process model into a more comprehensive eight-process framework that better aligns with operational requirements and best practices.</p>

        <a href="presentation.html" class="button">Launch Presentation</a>

        <div class="files">
            <h3>Available Files</h3>
            <ul>
                <li><strong>presentation.html</strong> - The main HTML presentation (can be opened directly)</li>
                <li><strong>custom.css</strong> - Styling for the presentation</li>
                <li><strong>custom-navigation.css</strong> - Navigation styling</li>
                <li><strong>custom-navigation.js</strong> - Navigation functionality</li>
                <li><strong>lib/</strong> - Directory containing all required Reveal.js files</li>
                <li><strong>README.md</strong> - Documentation and usage instructions</li>
            </ul>
        </div>

        <h2>How to Use</h2>
        <p>Simply click the "Launch Presentation" button above or open the presentation.html file directly in your browser.</p>
        <p>This presentation is fully self-contained and does not require a server to run.</p>

        <p>Navigate through the presentation using arrow keys or space bar.</p>
    </div>
</body>
</html>
