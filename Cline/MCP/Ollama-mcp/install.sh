#!/usr/bin/bash
set -e

# Create a new empty subdirectory for cloning
mkdir clone-dir
cd clone-dir

# Clone the repository into the new directory
git clone https://github.com/NightTrek/Ollama-mcp.git .

# Remove the existing .git folder to avoid duplication
rm -rf .git

# Install pnpm and dependencies
pnpm install

# Build the server
pnpm run build

# Update MCP settings
echo "{
  \"mcpServers\": {
    \"github.com/NightTrek/Ollama-mcp\": {
      \"command\": \"node\",
      \"args\": [\"/home/<USER>/Documents/Cline/MCP/Ollama-mcp/build/index.js\"],
      \"env\": {
        \"OLLAMA_HOST\": \"http://127.0.0.1:11434\"
      }
    }
  }
}" > /home/<USER>/.config/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json

echo " Installation completed successfully!"
