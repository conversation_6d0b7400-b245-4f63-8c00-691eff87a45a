[project]
name = "excel-mcp-server"
version = "0.1.0"
description = "MCP server for Excel file manipulation"
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "mcp[cli]>=1.2.0",
    "openpyxl>=3.1.2"
]
[[project.authors]]
name = "haris"
email = "<EMAIL>"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
excel-mcp-server = "excel_mcp.__main__:main"

[tool.hatch.build.targets.wheel]
packages = ["src/excel_mcp"]

[tool.hatch.build]
packages = ["src/excel_mcp"]
