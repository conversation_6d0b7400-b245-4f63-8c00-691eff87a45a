# Smithery configuration file: https://smithery.ai/docs/config#smitheryyaml

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required: []
    properties:
      excelFilesPath:
        type: string
        description: Directory where Excel files will be stored.
  commandFunction:
    # A function that produces the CLI command to start the MCP on stdio.
    |-
    config => ({ command: 'uv', args: ['run', 'excel-mcp-server'], env: { EXCEL_FILES_PATH: config.excelFilesPath || './excel_files' } })
