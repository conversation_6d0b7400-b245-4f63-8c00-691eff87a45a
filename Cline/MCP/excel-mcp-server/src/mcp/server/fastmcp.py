class FastMCP:
    """Mock FastMCP class to satisfy import requirements."""
    
    def __init__(self, *args, **kwargs):
        pass
        
    def run(self, *args, **kwargs):
        pass
        
    async def run_sse_async(self, *args, **kwargs):
        print("Mock run_sse_async method called")
        
    async def shutdown(self):
        print("Mock shutdown method called")
        
    def tool(self, *args, **kwargs):
        """Mock tool decorator."""
        def decorator(func):
            return func
        return decorator
