import logging
import re
from typing import Any, Dict, Optional

from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.worksheet import Worksheet

from .cell_utils import parse_cell_range, validate_cell_reference
from .exceptions import ValidationError

logger = logging.getLogger(__name__)

def validate_formula_in_cell_operation(
    filepath: str,
    sheet_name: str,
    cell: str,
    formula: str
) -> Dict[str, Any]:
    """Validate Excel formula before writing"""
    try:
        wb = load_workbook(filepath)
        if sheet_name not in wb.sheetnames:
            raise ValidationError(f"Sheet '{sheet_name}' not found")

        if not validate_cell_reference(cell):
            raise ValidationError(f"Invalid cell reference: {cell}")

        # First validate the provided formula's syntax
        is_valid, message = validate_formula(formula)
        if not is_valid:
            raise ValidationError(f"Invalid formula syntax: {message}")

        # Additional validation for cell references in formula
        cell_refs = re.findall(r'[A-Z]+[0-9]+(?::[A-Z]+[0-9]+)?', formula)
        for ref in cell_refs:
            if ':' in ref:  # Range reference
                start, end = ref.split(':')
                if not (validate_cell_reference(start) and validate_cell_reference(end)):
                    raise ValidationError(f"Invalid cell range reference in formula: {ref}")
            else:  # Single cell reference
                if not validate_cell_reference(ref):
                    raise ValidationError(f"Invalid cell reference in formula: {ref}")

        # Now check if there's a formula in the cell and compare
        sheet = wb[sheet_name]
        cell_obj = sheet[cell]
        current_formula = cell_obj.value

        # If cell has a formula (starts with =)
        if isinstance(current_formula, str) and current_formula.startswith('='):
            if formula.startswith('='):
                if current_formula != formula:
                    return {
                        "message": "Formula is valid but doesn't match cell content",
                        "valid": True,
                        "matches": False,
                        "cell": cell,
                        "provided_formula": formula,
                        "current_formula": current_formula
                    }
            else:
                if current_formula != f"={formula}":
                    return {
                        "message": "Formula is valid but doesn't match cell content",
                        "valid": True,
                        "matches": False,
                        "cell": cell,
                        "provided_formula": formula,
                        "current_formula": current_formula
                    }
                else:
                    return {
                        "message": "Formula is valid and matches cell content",
                        "valid": True,
                        "matches": True,
                        "cell": cell,
                        "formula": formula
                    }
        else:
            return {
                "message": "Formula is valid but cell contains no formula",
                "valid": True,
                "matches": False,
                "cell": cell,
                "provided_formula": formula,
                "current_content": str(current_formula) if current_formula else ""
            }

    except ValidationError as e:
        logger.error(str(e))
        raise
    except Exception as e:
        logger.error(f"Failed to validate formula: {e}")
        raise ValidationError(str(e))

def validate_range_in_sheet_operation(
    filepath: str,
    sheet_name: str,
    start_cell: str,
    end_cell: Optional[str] = None
) -> Dict[str, Any]:
    """Validate if a range exists in a worksheet and return data range info."""
    try:
        wb = load_workbook(filepath)
        if sheet_name not in wb.sheetnames:
            raise ValidationError(f"Sheet '{sheet_name}' not found")

        worksheet = wb[sheet_name]
        
        # Rest of the function implementation...
        return {"valid": True, "message": "Range is valid"}
        
    except Exception as e:
        logger.error(f"Failed to validate range: {e}")
        raise ValidationError(str(e))

def validate_formula(formula: str) -> tuple[bool, str]:
    """Basic validation of Excel formula syntax."""
    # This is a simplified validation
    if not formula:
        return False, "Empty formula"
        
    # If formula doesn't start with =, it's not technically a formula
    if not formula.startswith('=') and not formula.startswith('+') and not formula.startswith('-'):
        # Not an error, just a note
        return True, "Formula doesn't start with =, +, or -"
        
    # Check for balanced parentheses
    if formula.count('(') != formula.count(')'):
        return False, "Unbalanced parentheses"
        
    # Check for balanced quotes
    if formula.count('"') % 2 != 0:
        return False, "Unbalanced quotes"
        
    return True, "Formula syntax is valid"
