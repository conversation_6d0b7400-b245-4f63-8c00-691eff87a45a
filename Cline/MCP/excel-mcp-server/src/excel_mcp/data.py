from pathlib import Path
from typing import Any, Union, Optional, List, Dict
import logging

from openpyxl import load_workbook
from openpyxl.styles import Font
from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.utils import get_column_letter

from .exceptions import DataError
from .cell_utils import parse_cell_range

logger = logging.getLogger(__name__)

def read_excel_range(
    filepath: Union[Path, str],
    sheet_name: str,
    start_cell: str = "A1",
    end_cell: Optional[str] = None,
    preview_only: bool = False
) -> List[List[Any]]:
    """Read data from Excel worksheet range."""
    try:
        wb = load_workbook(filepath, read_only=True, data_only=True)
        if sheet_name not in wb.sheetnames:
            raise DataError(f"Sheet '{sheet_name}' not found")
            
        sheet = wb[sheet_name]
        
        # Parse cell references
        start_row, start_col, end_row, end_col = parse_cell_range(start_cell, end_cell)
        
        # If end_row/end_col not specified, use sheet dimensions
        if end_row is None:
            end_row = sheet.max_row
        if end_col is None:
            end_col = sheet.max_column
            
        # Read data
        data = []
        for row in range(start_row, end_row + 1):
            row_data = []
            for col in range(start_col, end_col + 1):
                cell_value = sheet.cell(row=row, column=col).value
                row_data.append(cell_value)
            data.append(row_data)
            
        # For preview, limit to first 10 rows
        if preview_only and len(data) > 10:
            data = data[:10]
            
        return data
        
    except Exception as e:
        logger.error(f"Failed to read Excel data: {e}")
        raise DataError(str(e))

def write_data(
    filepath: Union[Path, str],
    sheet_name: str,
    data: List[Dict[str, Any]],
    start_cell: str = "A1",
    write_headers: bool = True
) -> Dict[str, Any]:
    """Write data to Excel worksheet."""
    try:
        # Load workbook
        wb = load_workbook(filepath)
        if sheet_name not in wb.sheetnames:
            raise DataError(f"Sheet '{sheet_name}' not found")
            
        sheet = wb[sheet_name]
        
        # Parse start cell
        start_row, start_col, _, _ = parse_cell_range(start_cell)
        
        # Get headers from first dict
        if not data:
            raise DataError("No data to write")
            
        headers = list(data[0].keys())
        
        # Write headers
        current_row = start_row
        if write_headers:
            for i, header in enumerate(headers):
                col = start_col + i
                cell = sheet.cell(row=current_row, column=col)
                cell.value = header
                cell.font = Font(bold=True)
            current_row += 1
            
        # Write data
        for row_data in data:
            for i, header in enumerate(headers):
                col = start_col + i
                cell = sheet.cell(row=current_row, column=col)
                cell.value = row_data.get(header)
            current_row += 1
            
        # Save workbook
        wb.save(filepath)
        
        return {
            "message": f"Data written successfully to {sheet_name}",
            "rows_written": len(data) + (1 if write_headers else 0),
            "start_cell": start_cell,
            "end_cell": f"{get_column_letter(start_col + len(headers) - 1)}{current_row - 1}"
        }
        
    except Exception as e:
        logger.error(f"Failed to write Excel data: {e}")
        raise DataError(str(e))
