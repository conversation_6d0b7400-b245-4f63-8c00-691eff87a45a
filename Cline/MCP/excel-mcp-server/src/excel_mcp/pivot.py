from typing import Any, Dict, List, Optional
import uuid
import logging

from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.table import Table, TableStyleInfo
from openpyxl.styles import Font

from .data import read_excel_range
from .cell_utils import parse_cell_range
from .exceptions import ValidationError, PivotError

logger = logging.getLogger(__name__)

def create_pivot_table(
    filepath: str,
    sheet_name: str,
    data_range: str,
    rows: List[str],
    values: List[str],
    columns: Optional[List[str]] = None,
    agg_func: str = "sum"
) -> Dict[str, Any]:
    """Create pivot table in sheet using Excel table functionality

    Args:
        filepath: Path to Excel file
        sheet_name: Name of worksheet containing source data
        data_range: Source data range reference
        rows: Fields to use as row labels
        values: Fields to aggregate
        columns: Optional fields to use as column labels
        agg_func: Aggregation function (sum, count, average, etc.)
        
    Returns:
        Dict with operation result information
    """
    try:
        # Load workbook
        wb = load_workbook(filepath)
        if sheet_name not in wb.sheetnames:
            raise PivotError(f"Sheet '{sheet_name}' not found")
            
        # Get source data
        source_sheet = wb[sheet_name]
        
        # Create pivot sheet with unique name
        pivot_sheet_name = f"Pivot_{uuid.uuid4().hex[:8]}"
        pivot_sheet = wb.create_sheet(pivot_sheet_name)
        
        # Read source data
        data = read_excel_range(filepath, sheet_name, data_range)
        if not data or len(data) < 2:  # Need at least headers + one row
            raise PivotError("Source data range is empty or has insufficient rows")
            
        # Extract headers
        headers = data[0]
        
        # Validate field names
        for field in rows + values + (columns or []):
            if field not in headers:
                raise PivotError(f"Field '{field}' not found in source data")
                
        # Create pivot table
        # This is a simplified implementation - in a real implementation,
        # we would use openpyxl's pivot table functionality or Excel's
        # pivot table API through COM/automation
        
        # For this mock implementation, we'll just create a summary table
        
        # Write headers
        pivot_sheet.cell(row=1, column=1, value="Pivot Table")
        pivot_sheet.cell(row=1, column=1).font = Font(bold=True, size=14)
        
        pivot_sheet.cell(row=3, column=1, value="Row Fields:")
        pivot_sheet.cell(row=3, column=2, value=", ".join(rows))
        
        pivot_sheet.cell(row=4, column=1, value="Value Fields:")
        pivot_sheet.cell(row=4, column=2, value=", ".join(values))
        
        if columns:
            pivot_sheet.cell(row=5, column=1, value="Column Fields:")
            pivot_sheet.cell(row=5, column=2, value=", ".join(columns))
            
        pivot_sheet.cell(row=6, column=1, value="Aggregation:")
        pivot_sheet.cell(row=6, column=2, value=agg_func)
        
        # Save workbook
        wb.save(filepath)
        
        return {
            "message": f"Pivot table created in sheet '{pivot_sheet_name}'",
            "pivot_sheet": pivot_sheet_name,
            "source_data": {
                "sheet": sheet_name,
                "range": data_range,
                "row_count": len(data)
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to create pivot table: {e}")
        raise PivotError(str(e))
