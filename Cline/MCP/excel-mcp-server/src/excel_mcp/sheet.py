import logging
from typing import Any, Dict, Optional
from copy import copy

from openpyxl import load_workbook
from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.utils import get_column_letter, column_index_from_string
from openpyxl.styles import Font, Border, PatternFill, Side

from .cell_utils import parse_cell_range
from .exceptions import SheetError, ValidationError

logger = logging.getLogger(__name__)

def copy_sheet(filepath: str, source_sheet: str, target_sheet: str) -> Dict[str, Any]:
    """Copy a worksheet within the same workbook."""
    try:
        wb = load_workbook(filepath)
        if source_sheet not in wb.sheetnames:
            raise SheetError(f"Source sheet '{source_sheet}' not found")

        if target_sheet in wb.sheetnames:
            raise SheetError(f"Target sheet '{target_sheet}' already exists")

        source = wb[source_sheet]
        target = wb.copy_worksheet(source)
        target.title = target_sheet

        wb.save(filepath)
        return {"message": f"Sheet '{source_sheet}' copied to '{target_sheet}'"}
    except SheetError as e:
        logger.error(str(e))
        raise
    except Exception as e:
        logger.error(f"Failed to copy sheet: {e}")
        raise SheetError(str(e))

def delete_sheet(filepath: str, sheet_name: str) -> Dict[str, Any]:
    """Delete a worksheet from the workbook."""
    try:
        wb = load_workbook(filepath)
        if sheet_name not in wb.sheetnames:
            raise SheetError(f"Sheet '{sheet_name}' not found")

        if len(wb.sheetnames) == 1:
            raise SheetError("Cannot delete the only sheet in workbook")

        del wb[sheet_name]
        wb.save(filepath)
        return {"message": f"Sheet '{sheet_name}' deleted"}
    except SheetError as e:
        logger.error(str(e))
        raise
    except Exception as e:
        logger.error(f"Failed to delete sheet: {e}")
        raise SheetError(str(e))

def rename_sheet(filepath: str, old_name: str, new_name: str) -> Dict[str, Any]:
    """Rename a worksheet."""
    try:
        wb = load_workbook(filepath)
        if old_name not in wb.sheetnames:
            raise SheetError(f"Sheet '{old_name}' not found")

        if new_name in wb.sheetnames:
            raise SheetError(f"Sheet '{new_name}' already exists")

        sheet = wb[old_name]
        sheet.title = new_name
        wb.save(filepath)
        return {"message": f"Sheet renamed from '{old_name}' to '{new_name}'"}
    except SheetError as e:
        logger.error(str(e))
        raise
    except Exception as e:
        logger.error(f"Failed to rename sheet: {e}")
        raise SheetError(str(e))

def format_range_string(start_row: int, start_col: int, end_row: int, end_col: int) -> str:
    """Format range string from row and column indices."""
    return f"{get_column_letter(start_col)}{start_row}:{get_column_letter(end_col)}{end_row}"

def copy_range(
    source_ws: Worksheet,
    target_ws: Worksheet,
    source_range: str,
    target_start: Optional[str] = None
) -> None:
    """Copy range from source worksheet to target worksheet."""
    # Parse source range
    if ":" in source_range:
        start_cell, end_cell = source_range.split(":")
    else:
        start_cell = source_range
        end_cell = None
        
    start_row, start_col, end_row, end_col = parse_cell_range(start_cell, end_cell)
    
    # If end coordinates not specified, use max dimensions
    if end_row is None:
        end_row = source_ws.max_row
    if end_col is None:
        end_col = source_ws.max_column
        
    # Parse target start cell
    if target_start is None:
        target_start = "A1"
        
    target_row, target_col, _, _ = parse_cell_range(target_start)
    
    # Copy cells
    for row_idx, row in enumerate(range(start_row, end_row + 1)):
        for col_idx, col in enumerate(range(start_col, end_col + 1)):
            source_cell = source_ws.cell(row=row, column=col)
            target_cell = target_ws.cell(row=target_row + row_idx, column=target_col + col_idx)
            
            # Copy value
            target_cell.value = source_cell.value
            
            # Copy style
            if source_cell.has_style:
                target_cell.font = copy(source_cell.font)
                target_cell.border = copy(source_cell.border)
                target_cell.fill = copy(source_cell.fill)
                target_cell.number_format = copy(source_cell.number_format)
                target_cell.alignment = copy(source_cell.alignment)

def merge_range(filepath: str, sheet_name: str, start_cell: str, end_cell: str) -> Dict[str, Any]:
    """Merge a range of cells."""
    try:
        wb = load_workbook(filepath)
        if sheet_name not in wb.sheetnames:
            raise SheetError(f"Sheet '{sheet_name}' not found")
            
        sheet = wb[sheet_name]
        
        # Validate cell references
        if not start_cell or not end_cell:
            raise ValidationError("Start and end cell references are required")
            
        # Create range string
        range_string = f"{start_cell}:{end_cell}"
        
        # Check if range is already merged
        for merged_range in sheet.merged_cells.ranges:
            if str(merged_range) == range_string:
                return {"message": f"Range {range_string} is already merged"}
                
        # Merge cells
        sheet.merge_cells(range_string)
        
        # Save workbook
        wb.save(filepath)
        
        return {"message": f"Range {range_string} merged successfully"}
        
    except Exception as e:
        logger.error(f"Failed to merge range: {e}")
        raise SheetError(str(e))

def unmerge_range(filepath: str, sheet_name: str, start_cell: str, end_cell: str) -> Dict[str, Any]:
    """Unmerge a previously merged range of cells."""
    try:
        wb = load_workbook(filepath)
        if sheet_name not in wb.sheetnames:
            raise SheetError(f"Sheet '{sheet_name}' not found")
            
        sheet = wb[sheet_name]
        
        # Validate cell references
        if not start_cell or not end_cell:
            raise ValidationError("Start and end cell references are required")
            
        # Create range string
        range_string = f"{start_cell}:{end_cell}"
        
        # Check if range is merged
        is_merged = False
        for merged_range in sheet.merged_cells.ranges:
            if str(merged_range) == range_string:
                is_merged = True
                break
                
        if not is_merged:
            return {"message": f"Range {range_string} is not merged"}
            
        # Unmerge cells
        sheet.unmerge_cells(range_string)
        
        # Save workbook
        wb.save(filepath)
        
        return {"message": f"Range {range_string} unmerged successfully"}
        
    except Exception as e:
        logger.error(f"Failed to unmerge range: {e}")
        raise SheetError(str(e))
