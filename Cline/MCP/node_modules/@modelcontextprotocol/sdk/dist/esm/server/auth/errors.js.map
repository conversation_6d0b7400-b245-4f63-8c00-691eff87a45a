{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../../../src/server/auth/errors.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,KAAK;IACnC,YACkB,SAAiB,EACjC,OAAe,EACC,QAAiB;QAEjC,KAAK,CAAC,OAAO,CAAC,CAAC;QAJC,cAAS,GAAT,SAAS,CAAQ;QAEjB,aAAQ,GAAR,QAAQ,CAAS;QAGjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,QAAQ,GAAuB;YACnC,KAAK,EAAE,IAAI,CAAC,SAAS;YACrB,iBAAiB,EAAE,IAAI,CAAC,OAAO;SAChC,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;QACrC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAED;;;;GAIG;AACH,MAAM,OAAO,mBAAoB,SAAQ,UAAU;IACjD,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,iBAAiB,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC9C,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,kBAAmB,SAAQ,UAAU;IAChD,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,gBAAgB,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC7C,CAAC;CACF;AAED;;;;GAIG;AACH,MAAM,OAAO,iBAAkB,SAAQ,UAAU;IAC/C,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,eAAe,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC5C,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,uBAAwB,SAAQ,UAAU;IACrD,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,qBAAqB,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAClD,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,yBAA0B,SAAQ,UAAU;IACvD,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,wBAAwB,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACrD,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,iBAAkB,SAAQ,UAAU;IAC/C,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,eAAe,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC5C,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,UAAU;IAC/C,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,eAAe,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC5C,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,WAAY,SAAQ,UAAU;IACzC,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,cAAc,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC3C,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,2BAA4B,SAAQ,UAAU;IACzD,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,yBAAyB,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACtD,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,4BAA6B,SAAQ,UAAU;IAC1D,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,2BAA2B,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACxD,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,yBAA0B,SAAQ,UAAU;IACvD,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,wBAAwB,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACrD,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,iBAAkB,SAAQ,UAAU;IAC/C,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,eAAe,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC5C,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,qBAAsB,SAAQ,UAAU;IACnD,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,oBAAoB,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACjD,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,oBAAqB,SAAQ,UAAU;IAClD,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,mBAAmB,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAChD,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,0BAA2B,SAAQ,UAAU;IACxD,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,yBAAyB,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACtD,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,sBAAuB,SAAQ,UAAU;IACpD,YAAY,OAAe,EAAE,QAAiB;QAC5C,KAAK,CAAC,oBAAoB,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACjD,CAAC;CACF"}